"""
AI Engine Main Server
FastAPI server for AI-powered mobile app development
"""

import os
import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAP<PERSON>, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from dotenv import load_dotenv

from api.chat import router as chat_router
from api.code_generation import router as code_router
from api.learning import router as learning_router
from api.projects import router as projects_router
from services.ai_orchestrator import AIOrchestrator
from services.learning_service import LearningService
from services.code_generator import CodeGenerator
from utils.logger import setup_logger
from utils.metrics import setup_metrics

# Load environment variables
load_dotenv()

# Setup logging
logger = setup_logger(__name__)

# Global services
ai_orchestrator: AIOrchestrator = None
learning_service: LearningService = None
code_generator: CodeGenerator = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global ai_orchestrator, learning_service, code_generator
    
    logger.info("🚀 Starting AI Engine...")
    
    try:
        # Initialize services
        ai_orchestrator = AIOrchestrator()
        learning_service = LearningService()
        code_generator = CodeGenerator()
        
        # Initialize AI models
        await ai_orchestrator.initialize()
        await learning_service.initialize()
        await code_generator.initialize()
        
        # Setup metrics
        setup_metrics()
        
        logger.info("✅ AI Engine initialized successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize AI Engine: {e}")
        raise
    finally:
        logger.info("🛑 Shutting down AI Engine...")
        
        # Cleanup services
        if ai_orchestrator:
            await ai_orchestrator.cleanup()
        if learning_service:
            await learning_service.cleanup()
        if code_generator:
            await code_generator.cleanup()


# Create FastAPI app
app = FastAPI(
    title="Mobile AI Platform - AI Engine",
    description="AI-powered mobile application development engine",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3003", "http://localhost:3004"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)


# Dependency to get services
async def get_ai_orchestrator() -> AIOrchestrator:
    if ai_orchestrator is None:
        raise HTTPException(status_code=503, detail="AI Orchestrator not initialized")
    return ai_orchestrator


async def get_learning_service() -> LearningService:
    if learning_service is None:
        raise HTTPException(status_code=503, detail="Learning Service not initialized")
    return learning_service


async def get_code_generator() -> CodeGenerator:
    if code_generator is None:
        raise HTTPException(status_code=503, detail="Code Generator not initialized")
    return code_generator


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "AI Engine",
        "version": "1.0.0",
        "models_loaded": ai_orchestrator.is_ready() if ai_orchestrator else False,
        "learning_active": learning_service.is_active() if learning_service else False,
        "code_gen_ready": code_generator.is_ready() if code_generator else False
    }


# Status endpoint
@app.get("/status")
async def get_status(
    orchestrator: AIOrchestrator = Depends(get_ai_orchestrator),
    learning: LearningService = Depends(get_learning_service),
    generator: CodeGenerator = Depends(get_code_generator)
):
    """Get detailed status of all AI services"""
    return {
        "ai_orchestrator": await orchestrator.get_status(),
        "learning_service": await learning.get_status(),
        "code_generator": await generator.get_status(),
        "system": {
            "memory_usage": await get_memory_usage(),
            "active_sessions": await get_active_sessions(),
            "requests_processed": await get_requests_count()
        }
    }


# Include API routers
app.include_router(chat_router, prefix="/api/chat", tags=["Chat"])
app.include_router(code_router, prefix="/api/code", tags=["Code Generation"])
app.include_router(learning_router, prefix="/api/learning", tags=["Learning"])
app.include_router(projects_router, prefix="/api/projects", tags=["Projects"])


# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    logger.error(f"HTTP Exception: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail, "status_code": exc.status_code}
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "status_code": 500}
    )


# Utility functions
async def get_memory_usage() -> Dict[str, Any]:
    """Get current memory usage"""
    import psutil
    process = psutil.Process()
    memory_info = process.memory_info()
    return {
        "rss": memory_info.rss,
        "vms": memory_info.vms,
        "percent": process.memory_percent()
    }


async def get_active_sessions() -> int:
    """Get number of active sessions"""
    if ai_orchestrator:
        return await ai_orchestrator.get_active_sessions_count()
    return 0


async def get_requests_count() -> int:
    """Get total requests processed"""
    if ai_orchestrator:
        return await ai_orchestrator.get_requests_count()
    return 0


# WebSocket endpoint for real-time communication
@app.websocket("/ws")
async def websocket_endpoint(websocket):
    """WebSocket endpoint for real-time AI communication"""
    await websocket.accept()
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_json()
            
            # Process with AI orchestrator
            if ai_orchestrator:
                response = await ai_orchestrator.process_websocket_message(data)
                await websocket.send_json(response)
            else:
                await websocket.send_json({
                    "error": "AI services not available",
                    "status": "error"
                })
                
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        await websocket.close()


if __name__ == "__main__":
    # Configuration
    host = os.getenv("AI_ENGINE_HOST", "0.0.0.0")
    port = int(os.getenv("AI_ENGINE_PORT", "8000"))
    workers = int(os.getenv("AI_ENGINE_WORKERS", "1"))
    log_level = os.getenv("AI_ENGINE_LOG_LEVEL", "info")
    
    logger.info(f"🚀 Starting AI Engine on {host}:{port}")
    
    # Run server
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        workers=workers,
        log_level=log_level,
        reload=os.getenv("AI_ENGINE_RELOAD", "false").lower() == "true"
    )
