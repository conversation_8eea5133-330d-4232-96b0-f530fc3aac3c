// Chat Types
export interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  attachments?: File[];
  metadata?: {
    tokens?: number;
    model?: string;
    confidence?: number;
  };
}

export interface Conversation {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  projectId?: string;
}

// Project Types
export interface Project {
  id: string;
  name: string;
  description: string;
  platform: 'React Native' | 'Flutter' | 'Android' | 'iOS';
  status: 'În dezvoltare' | 'Completat' | 'Pauzat';
  progress: number;
  lastModified: string;
  thumbnail: string;
  code?: {
    files: CodeFile[];
    mainFile: string;
  };
  settings: ProjectSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface CodeFile {
  id: string;
  name: string;
  path: string;
  content: string;
  language: 'typescript' | 'javascript' | 'kotlin' | 'swift' | 'dart';
  lastModified: Date;
}

export interface ProjectSettings {
  targetPlatform: 'android' | 'ios' | 'both';
  framework: 'react-native' | 'flutter' | 'native';
  packageName: string;
  version: string;
  buildConfig: {
    debug: boolean;
    minSdkVersion?: number;
    targetSdkVersion?: number;
    iosDeploymentTarget?: string;
  };
}

// AI Types
export interface AIStats {
  totalProjects: number;
  successRate: number;
  learningProgress: number;
  responseTime: number;
  knowledgeBase: number;
  lastUpdate: string;
  modelsUsed: string[];
  averageTokensPerResponse: number;
}

export interface LearningProgress {
  domains: LearningDomain[];
  overallProgress: number;
  recentLearning: RecentLearning[];
  trainingStatus: 'idle' | 'training' | 'optimizing';
}

export interface LearningDomain {
  name: string;
  level: number;
  color: string;
  topics: string[];
  lastUpdated: Date;
}

export interface RecentLearning {
  topic: string;
  confidence: number;
  date: string;
  source: 'conversation' | 'feedback' | 'training';
}

export interface AIConfiguration {
  creativity: number; // 0-100
  responseSpeed: 'fast' | 'balanced' | 'detailed';
  preferredLanguage: 'ro' | 'en' | 'both';
  preferredFramework: 'react-native' | 'flutter' | 'native' | 'auto-detect';
  enableLearning: boolean;
  proactiveSuggestions: boolean;
  debugMode: boolean;
  autoSave: boolean;
}

// Workspace Types
export interface WorkspaceState {
  currentProject?: Project;
  activeFile?: CodeFile;
  previewPlatform: 'android' | 'ios';
  isBuilding: boolean;
  buildLogs: BuildLog[];
  chatMessages: ChatMessage[];
}

export interface BuildLog {
  id: string;
  timestamp: Date;
  level: 'info' | 'warning' | 'error';
  message: string;
  source: 'compiler' | 'bundler' | 'simulator';
}

// Mobile Preview Types
export interface MobileSimulatorProps {
  platform: 'android' | 'ios';
  appCode: string;
  deviceType?: 'phone' | 'tablet';
  orientation?: 'portrait' | 'landscape';
  onInteraction?: (event: SimulatorEvent) => void;
}

export interface SimulatorEvent {
  type: 'tap' | 'swipe' | 'scroll' | 'input';
  target: string;
  coordinates?: { x: number; y: number };
  value?: string;
}

// API Types
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: Date;
}

export interface AIRequest {
  message: string;
  context?: {
    projectId?: string;
    currentCode?: string;
    platform?: string;
  };
  options?: {
    temperature?: number;
    maxTokens?: number;
    stream?: boolean;
  };
}

export interface AIResponse {
  message: string;
  suggestions?: string[];
  codeChanges?: CodeChange[];
  confidence: number;
  reasoning?: string;
}

export interface CodeChange {
  file: string;
  action: 'create' | 'update' | 'delete';
  content?: string;
  lineStart?: number;
  lineEnd?: number;
  description: string;
}

// WebSocket Types
export interface WebSocketMessage {
  type: 'chat' | 'code-update' | 'build-status' | 'ai-thinking';
  payload: any;
  timestamp: Date;
  sessionId: string;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  userId?: string;
  projectId?: string;
}

// User Types (for future authentication)
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  preferences: UserPreferences;
  subscription: 'free' | 'pro' | 'enterprise';
  createdAt: Date;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: 'ro' | 'en';
  notifications: {
    email: boolean;
    push: boolean;
    buildComplete: boolean;
    aiUpdates: boolean;
  };
  editor: {
    fontSize: number;
    tabSize: number;
    wordWrap: boolean;
    minimap: boolean;
  };
}

// Utility Types
export type Platform = 'android' | 'ios' | 'web';
export type Framework = 'react-native' | 'flutter' | 'native';
export type BuildStatus = 'idle' | 'building' | 'success' | 'error';
export type AIModel = 'gpt-4' | 'gpt-3.5-turbo' | 'claude-3' | 'local';

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface LoadingState {
  isLoading: boolean;
  message?: string;
  progress?: number;
}
