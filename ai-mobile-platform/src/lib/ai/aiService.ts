import { AIRequest, AIResponse, ChatMessage, CodeChange, Project } from "@/types";

class AIService {
  private baseUrl = process.env.NEXT_PUBLIC_AI_API_URL || 'http://localhost:3004/api/ai';
  private apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY;

  async sendMessage(request: AIRequest): Promise<AIResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new Error(`AI Service error: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('AI Service error:', error);

      // Fallback to mock response for development
      return this.getMockResponse(request);
    }
  }

  async generateCode(prompt: string, platform: string, context?: any): Promise<CodeChange[]> {
    try {
      const response = await fetch(`${this.baseUrl}/generate-code`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({ prompt, platform, context })
      });

      if (!response.ok) {
        throw new Error(`Code generation error: ${response.statusText}`);
      }

      const data = await response.json();
      return data.codeChanges || [];
    } catch (error) {
      console.error('Code generation error:', error);

      // Fallback to mock code generation
      return this.getMockCodeGeneration(prompt, platform);
    }
  }

  async analyzeProject(project: Project): Promise<{
    suggestions: string[];
    optimizations: string[];
    issues: string[];
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/analyze-project`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({ project })
      });

      if (!response.ok) {
        throw new Error(`Project analysis error: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Project analysis error:', error);

      return {
        suggestions: [
          "Adaugă validare pentru input-urile utilizatorului",
          "Implementează caching pentru performanță mai bună",
          "Adaugă teste unitare pentru componentele principale"
        ],
        optimizations: [
          "Optimizează imaginile pentru încărcare mai rapidă",
          "Folosește lazy loading pentru componentele mari",
          "Implementează code splitting"
        ],
        issues: [
          "Lipsesc verificările de erori în unele funcții",
          "Unele componente nu sunt responsive"
        ]
      };
    }
  }

  async improveCode(code: string, improvements: string[]): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/improve-code`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({ code, improvements })
      });

      if (!response.ok) {
        throw new Error(`Code improvement error: ${response.statusText}`);
      }

      const data = await response.json();
      return data.improvedCode || code;
    } catch (error) {
      console.error('Code improvement error:', error);
      return code; // Return original code if improvement fails
    }
  }

  private getMockResponse(request: AIRequest): AIResponse {
    const responses = [
      {
        message: `Excelentă idee! Pentru "${request.message}", îți sugerez să începem cu o arhitectură React Native pentru a acoperi atât Android cât și iOS. Vrei să discutăm despre funcționalitățile principale sau să trecem direct la dezvoltare?`,
        suggestions: [
          "Să definim funcționalitățile principale",
          "Să creez structura de bază a proiectului",
          "Să discutăm despre design și UI"
        ],
        confidence: 0.92
      },
      {
        message: `Perfect! Voi crea o aplicație ${this.detectAppType(request.message)} cu toate funcționalitățile pe care le-ai menționat. Să încep cu structura de bază și apoi să adaug funcționalitățile una câte una.`,
        suggestions: [
          "Începe cu structura de bază",
          "Adaugă funcționalitățile principale",
          "Configurează stilizarea"
        ],
        confidence: 0.88
      },
      {
        message: `Am înțeles cerințele tale. Voi implementa această funcționalitate pas cu pas. Poți urmări progresul în editorul de cod din dreapta.`,
        suggestions: [
          "Continuă cu implementarea",
          "Testează funcționalitatea",
          "Adaugă validări"
        ],
        confidence: 0.95
      }
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  }

  private getMockCodeGeneration(prompt: string, platform: string): CodeChange[] {
    const appType = this.detectAppType(prompt);

    if (appType === 'ecommerce') {
      return [
        {
          file: 'App.tsx',
          action: 'create',
          content: this.getEcommerceAppCode(),
          description: 'Aplicația principală de e-commerce cu navigare și componente de bază'
        },
        {
          file: 'components/ProductCard.tsx',
          action: 'create',
          content: this.getProductCardCode(),
          description: 'Componentă pentru afișarea produselor'
        }
      ];
    } else if (appType === 'fitness') {
      return [
        {
          file: 'App.tsx',
          action: 'create',
          content: this.getFitnessAppCode(),
          description: 'Aplicația de fitness cu dashboard și tracking'
        }
      ];
    } else if (appType === 'chat') {
      return [
        {
          file: 'App.tsx',
          action: 'create',
          content: this.getChatAppCode(),
          description: 'Aplicația de chat cu lista de conversații'
        }
      ];
    }

    return [
      {
        file: 'App.tsx',
        action: 'create',
        content: this.getDefaultAppCode(),
        description: 'Aplicația de bază cu structura principală'
      }
    ];
  }

  private detectAppType(prompt: string): string {
    const lowerPrompt = prompt.toLowerCase();

    if (lowerPrompt.includes('magazin') || lowerPrompt.includes('ecommerce') || lowerPrompt.includes('shopping')) {
      return 'ecommerce';
    } else if (lowerPrompt.includes('fitness') || lowerPrompt.includes('antrenament') || lowerPrompt.includes('sport')) {
      return 'fitness';
    } else if (lowerPrompt.includes('chat') || lowerPrompt.includes('mesagerie') || lowerPrompt.includes('social')) {
      return 'chat';
    }

    return 'default';
  }

  private getEcommerceAppCode(): string {
    return `import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Image } from 'react-native';

const ShoppingApp = () => {
  const [products, setProducts] = useState([
    { id: 1, name: 'Telefon Samsung Galaxy S24', price: 2500, image: 'phone.jpg', rating: 4.5 },
    { id: 2, name: 'Laptop Dell XPS 13', price: 3500, image: 'laptop.jpg', rating: 4.8 },
    { id: 3, name: 'Căști Sony WH-1000XM5', price: 450, image: 'headphones.jpg', rating: 4.7 },
    { id: 4, name: 'Smartwatch Apple Watch', price: 1200, image: 'watch.jpg', rating: 4.6 }
  ]);

  const [cart, setCart] = useState([]);

  const addToCart = (product) => {
    setCart([...cart, product]);
  };

  const renderProduct = ({ item }) => (
    <View style={styles.productCard}>
      <View style={styles.productImageContainer}>
        <Text style={styles.productImagePlaceholder}>📱</Text>
      </View>
      <Text style={styles.productName}>{item.name}</Text>
      <Text style={styles.productPrice}>{item.price} RON</Text>
      <View style={styles.ratingContainer}>
        <Text style={styles.rating}>⭐ {item.rating}</Text>
      </View>
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => addToCart(item)}
      >
        <Text style={styles.buttonText}>Adaugă în coș</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Magazin Online</Text>
      <FlatList
        data={products}
        renderItem={renderProduct}
        keyExtractor={item => item.id.toString()}
        style={styles.productList}
        numColumns={2}
      />
      <View style={styles.cartInfo}>
        <Text style={styles.cartText}>Produse în coș: {cart.length}</Text>
        <TouchableOpacity style={styles.checkoutButton}>
          <Text style={styles.checkoutText}>Vezi coșul</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5'
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333'
  },
  productCard: {
    backgroundColor: 'white',
    padding: 15,
    margin: 5,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    flex: 1
  },
  productImageContainer: {
    alignItems: 'center',
    marginBottom: 10
  },
  productImagePlaceholder: {
    fontSize: 40
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5
  },
  productPrice: {
    fontSize: 18,
    color: '#e74c3c',
    fontWeight: 'bold',
    marginBottom: 5
  },
  ratingContainer: {
    marginBottom: 10
  },
  rating: {
    fontSize: 14,
    color: '#f39c12'
  },
  addButton: {
    backgroundColor: '#3498db',
    padding: 10,
    borderRadius: 8,
    alignItems: 'center'
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14
  },
  productList: {
    flex: 1
  },
  cartInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 12,
    marginTop: 10
  },
  cartText: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  checkoutButton: {
    backgroundColor: '#27ae60',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8
  },
  checkoutText: {
    color: 'white',
    fontWeight: 'bold'
  }
});

export default ShoppingApp;`;
  }

  private getProductCardCode(): string {
    return `import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';

interface ProductCardProps {
  product: {
    id: number;
    name: string;
    price: number;
    image: string;
    rating: number;
  };
  onAddToCart: (product: any) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onAddToCart }) => {
  return (
    <View style={styles.card}>
      <View style={styles.imageContainer}>
        <Text style={styles.imagePlaceholder}>📱</Text>
      </View>
      <Text style={styles.name}>{product.name}</Text>
      <Text style={styles.price}>{product.price} RON</Text>
      <Text style={styles.rating}>⭐ {product.rating}</Text>
      <TouchableOpacity
        style={styles.button}
        onPress={() => onAddToCart(product)}
      >
        <Text style={styles.buttonText}>Adaugă în coș</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    padding: 15,
    margin: 5,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    flex: 1
  },
  imageContainer: {
    alignItems: 'center',
    marginBottom: 10
  },
  imagePlaceholder: {
    fontSize: 40
  },
  name: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5
  },
  price: {
    fontSize: 18,
    color: '#e74c3c',
    fontWeight: 'bold',
    marginBottom: 5
  },
  rating: {
    fontSize: 14,
    color: '#f39c12',
    marginBottom: 10
  },
  button: {
    backgroundColor: '#3498db',
    padding: 10,
    borderRadius: 8,
    alignItems: 'center'
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14
  }
});

export default ProductCard;`;
  }

  private getFitnessAppCode(): string {
    return `import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';

const FitnessApp = () => {
  const [stats, setStats] = useState({
    steps: 1247,
    calories: 342,
    activeMinutes: 45,
    distance: 7.2
  });

  const [workouts, setWorkouts] = useState([
    { id: 1, name: 'Cardio Morning', duration: 30, calories: 250, completed: true },
    { id: 2, name: 'Strength Training', duration: 45, calories: 180, completed: false },
    { id: 3, name: 'Yoga Session', duration: 60, calories: 120, completed: false }
  ]);

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Fitness Tracker</Text>

      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.steps}</Text>
          <Text style={styles.statLabel}>Pași</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.calories}</Text>
          <Text style={styles.statLabel}>Calorii</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.activeMinutes}</Text>
          <Text style={styles.statLabel}>Min Active</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.distance}</Text>
          <Text style={styles.statLabel}>Km</Text>
        </View>
      </View>

      <TouchableOpacity style={styles.startWorkoutButton}>
        <Text style={styles.startWorkoutText}>Începe Antrenament</Text>
      </TouchableOpacity>

      <View style={styles.workoutsSection}>
        <Text style={styles.sectionTitle}>Antrenamente Planificate</Text>
        {workouts.map(workout => (
          <View key={workout.id} style={styles.workoutCard}>
            <View style={styles.workoutInfo}>
              <Text style={styles.workoutName}>{workout.name}</Text>
              <Text style={styles.workoutDetails}>
                {workout.duration} min • {workout.calories} cal
              </Text>
            </View>
            <TouchableOpacity
              style={[
                styles.workoutButton,
                workout.completed && styles.completedButton
              ]}
            >
              <Text style={styles.workoutButtonText}>
                {workout.completed ? '✓' : 'Start'}
              </Text>
            </TouchableOpacity>
          </View>
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa'
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginVertical: 20,
    color: '#2c3e50'
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginBottom: 20
  },
  statCard: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    width: '48%',
    marginBottom: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3498db'
  },
  statLabel: {
    fontSize: 14,
    color: '#7f8c8d',
    marginTop: 5
  },
  startWorkoutButton: {
    backgroundColor: '#e74c3c',
    marginHorizontal: 20,
    padding: 15,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 30
  },
  startWorkoutText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold'
  },
  workoutsSection: {
    paddingHorizontal: 20
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#2c3e50'
  },
  workoutCard: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 12,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  workoutInfo: {
    flex: 1
  },
  workoutName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50'
  },
  workoutDetails: {
    fontSize: 14,
    color: '#7f8c8d',
    marginTop: 2
  },
  workoutButton: {
    backgroundColor: '#27ae60',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 8
  },
  completedButton: {
    backgroundColor: '#95a5a6'
  },
  workoutButtonText: {
    color: 'white',
    fontWeight: 'bold'
  }
});

export default FitnessApp;`;
  }

  private getChatAppCode(): string {
    return `import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, TextInput } from 'react-native';

const ChatApp = () => {
  const [conversations, setConversations] = useState([
    { id: 1, name: 'Ana Maria', lastMessage: 'Salut! Ce mai faci?', time: '14:30', unread: 2 },
    { id: 2, name: 'Bogdan', lastMessage: 'Ne vedem mâine?', time: '13:15', unread: 0 },
    { id: 3, name: 'Cristina', lastMessage: 'Mulțumesc pentru ajutor!', time: '12:45', unread: 1 },
    { id: 4, name: 'David', lastMessage: 'Perfect, vorbim mai târziu', time: '11:20', unread: 0 }
  ]);

  const renderConversation = ({ item }) => (
    <TouchableOpacity style={styles.conversationItem}>
      <View style={styles.avatar}>
        <Text style={styles.avatarText}>{item.name[0]}</Text>
      </View>
      <View style={styles.conversationContent}>
        <View style={styles.conversationHeader}>
          <Text style={styles.contactName}>{item.name}</Text>
          <Text style={styles.messageTime}>{item.time}</Text>
        </View>
        <View style={styles.messagePreview}>
          <Text style={styles.lastMessage} numberOfLines={1}>
            {item.lastMessage}
          </Text>
          {item.unread > 0 && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadText}>{item.unread}</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Chat Social</Text>
        <TouchableOpacity style={styles.newChatButton}>
          <Text style={styles.newChatText}>+</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Caută conversații..."
          placeholderTextColor="#999"
        />
      </View>

      <FlatList
        data={conversations}
        renderItem={renderConversation}
        keyExtractor={item => item.id.toString()}
        style={styles.conversationsList}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa'
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef'
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50'
  },
  newChatButton: {
    backgroundColor: '#3498db',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center'
  },
  newChatText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold'
  },
  searchContainer: {
    padding: 15,
    backgroundColor: 'white'
  },
  searchInput: {
    backgroundColor: '#f1f3f4',
    padding: 12,
    borderRadius: 25,
    fontSize: 16
  },
  conversationsList: {
    flex: 1
  },
  conversationItem: {
    flexDirection: 'row',
    padding: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f1f3f4'
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#3498db',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15
  },
  avatarText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold'
  },
  conversationContent: {
    flex: 1
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5
  },
  contactName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50'
  },
  messageTime: {
    fontSize: 12,
    color: '#7f8c8d'
  },
  messagePreview: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  lastMessage: {
    fontSize: 14,
    color: '#7f8c8d',
    flex: 1
  },
  unreadBadge: {
    backgroundColor: '#e74c3c',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10
  },
  unreadText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold'
  }
});

export default ChatApp;`;
  }

  private getDefaultAppCode(): string {
    return `import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

const MyApp = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Aplicația Mea</Text>
      <Text style={styles.subtitle}>Bine ai venit!</Text>

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button}>
          <Text style={styles.buttonText}>Începe</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.button, styles.secondaryButton]}>
          <Text style={[styles.buttonText, styles.secondaryButtonText]}>Setări</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    padding: 20
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 10
  },
  subtitle: {
    fontSize: 16,
    color: '#7f8c8d',
    marginBottom: 40
  },
  buttonContainer: {
    width: '100%',
    maxWidth: 300
  },
  button: {
    backgroundColor: '#3498db',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 15
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold'
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#3498db'
  },
  secondaryButtonText: {
    color: '#3498db'
  }
});

export default MyApp;`;
  }
}

export const aiService = new AIService();