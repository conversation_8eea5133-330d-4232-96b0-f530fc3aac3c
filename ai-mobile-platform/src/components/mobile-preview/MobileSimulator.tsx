"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Smartphone, 
  Tablet, 
  RotateCcw, 
  Volume2, 
  Wifi, 
  Battery, 
  Signal,
  Home,
  ArrowLeft,
  MoreVertical,
  RefreshCw
} from "lucide-react";
import { MobileSimulatorProps, SimulatorEvent } from "@/types";

interface DeviceFrame {
  name: string;
  width: number;
  height: number;
  borderRadius: string;
  statusBarHeight: number;
  homeIndicatorHeight: number;
}

const devices: Record<string, DeviceFrame> = {
  'iphone-14': {
    name: 'iPhone 14',
    width: 280,
    height: 600,
    borderRadius: '2.5rem',
    statusBarHeight: 44,
    homeIndicatorHeight: 34
  },
  'pixel-7': {
    name: 'Pixel 7',
    width: 280,
    height: 600,
    borderRadius: '2rem',
    statusBarHeight: 32,
    homeIndicatorHeight: 0
  },
  'ipad': {
    name: 'iPad',
    width: 400,
    height: 600,
    borderRadius: '1.5rem',
    statusBarHeight: 32,
    homeIndicatorHeight: 20
  }
};

export function MobileSimulator({
  platform,
  appCode,
  deviceType = 'phone',
  orientation = 'portrait',
  onInteraction
}: MobileSimulatorProps) {
  const [selectedDevice, setSelectedDevice] = useState<string>(
    platform === 'ios' ? 'iphone-14' : 'pixel-7'
  );
  const [isLoading, setIsLoading] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [appContent, setAppContent] = useState<any>(null);

  const device = devices[selectedDevice];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    // Simulate app compilation and rendering
    setIsLoading(true);
    setTimeout(() => {
      setAppContent(generateAppPreview(appCode, platform));
      setIsLoading(false);
    }, 1000);
  }, [appCode, platform]);

  const generateAppPreview = (code: string, platform: string) => {
    // This would normally parse the code and generate a preview
    // For now, we'll return a mock preview based on the code content
    if (code.includes('ShoppingApp') || code.includes('ecommerce') || code.includes('shopping')) {
      return {
        type: 'ecommerce',
        title: 'Magazin Online',
        screens: ['ProductList', 'ProductDetail', 'Cart', 'Checkout']
      };
    } else if (code.includes('FitnessApp') || code.includes('fitness') || code.includes('workout')) {
      return {
        type: 'fitness',
        title: 'Fitness Tracker',
        screens: ['Dashboard', 'Workouts', 'Progress', 'Settings']
      };
    } else if (code.includes('ChatApp') || code.includes('messaging') || code.includes('chat')) {
      return {
        type: 'chat',
        title: 'Chat Social',
        screens: ['ChatList', 'ChatDetail', 'Profile', 'Settings']
      };
    }
    
    return {
      type: 'default',
      title: 'Aplicația Mea',
      screens: ['Home', 'Settings']
    };
  };

  const handleDeviceInteraction = (event: React.MouseEvent, elementId: string) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const simulatorEvent: SimulatorEvent = {
      type: 'tap',
      target: elementId,
      coordinates: { x, y }
    };

    onInteraction?.(simulatorEvent);
  };

  const renderStatusBar = () => (
    <div className="flex items-center justify-between px-4 py-2 text-xs font-medium">
      <div className="flex items-center space-x-1">
        <span>{currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
      </div>
      <div className="flex items-center space-x-1">
        <Signal className="h-3 w-3" />
        <Wifi className="h-3 w-3" />
        <Battery className="h-3 w-3" />
        <span>100%</span>
      </div>
    </div>
  );

  const renderNavigationBar = () => (
    <div className="flex items-center justify-between px-4 py-3 border-t bg-gray-50 dark:bg-gray-800">
      <Button
        variant="ghost"
        size="sm"
        onClick={(e) => handleDeviceInteraction(e, 'nav-back')}
      >
        <ArrowLeft className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={(e) => handleDeviceInteraction(e, 'nav-home')}
      >
        <Home className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={(e) => handleDeviceInteraction(e, 'nav-menu')}
      >
        <MoreVertical className="h-4 w-4" />
      </Button>
    </div>
  );

  const renderAppContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-sm text-gray-600">Compilez aplicația...</p>
          </div>
        </div>
      );
    }

    if (!appContent) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <Smartphone className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-sm text-gray-600">Aplicația va apărea aici</p>
          </div>
        </div>
      );
    }

    // Render different app types
    switch (appContent.type) {
      case 'ecommerce':
        return renderEcommerceApp();
      case 'fitness':
        return renderFitnessApp();
      case 'chat':
        return renderChatApp();
      default:
        return renderDefaultApp();
    }
  };

  const renderEcommerceApp = () => (
    <div className="p-4 space-y-4">
      <h2 className="text-lg font-bold text-center">Magazin Online</h2>
      <div className="space-y-3">
        {['Telefon Samsung', 'Laptop Dell', 'Căști Sony'].map((product, index) => (
          <div
            key={index}
            className="bg-gray-50 dark:bg-gray-800 p-3 rounded border cursor-pointer"
            onClick={(e) => handleDeviceInteraction(e, `product-${index}`)}
          >
            <h3 className="font-semibold text-sm">{product}</h3>
            <p className="text-gray-600 text-xs">{2500 + index * 500} RON</p>
            <button className="bg-blue-500 text-white px-3 py-1 rounded text-xs mt-2">
              Adaugă în coș
            </button>
          </div>
        ))}
      </div>
      <div className="bg-white dark:bg-gray-700 p-3 rounded border">
        <p className="text-sm">Produse în coș: 0</p>
      </div>
    </div>
  );

  const renderFitnessApp = () => (
    <div className="p-4 space-y-4">
      <h2 className="text-lg font-bold text-center">Fitness Tracker</h2>
      <div className="grid grid-cols-2 gap-3">
        <div className="bg-blue-100 p-3 rounded text-center">
          <div className="text-2xl font-bold">1,247</div>
          <div className="text-xs">Pași</div>
        </div>
        <div className="bg-red-100 p-3 rounded text-center">
          <div className="text-2xl font-bold">342</div>
          <div className="text-xs">Calorii</div>
        </div>
        <div className="bg-green-100 p-3 rounded text-center">
          <div className="text-2xl font-bold">45</div>
          <div className="text-xs">Min Active</div>
        </div>
        <div className="bg-purple-100 p-3 rounded text-center">
          <div className="text-2xl font-bold">7.2</div>
          <div className="text-xs">Km</div>
        </div>
      </div>
      <button className="w-full bg-blue-600 text-white py-3 rounded font-semibold">
        Începe Antrenament
      </button>
    </div>
  );

  const renderChatApp = () => (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b">
        <h2 className="text-lg font-bold">Chat Social</h2>
      </div>
      <div className="flex-1 p-4 space-y-3">
        {['Ana Maria', 'Bogdan', 'Cristina'].map((name, index) => (
          <div
            key={index}
            className="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded cursor-pointer"
            onClick={(e) => handleDeviceInteraction(e, `chat-${index}`)}
          >
            <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm">
              {name[0]}
            </div>
            <div className="flex-1">
              <div className="font-semibold text-sm">{name}</div>
              <div className="text-xs text-gray-600">Mesaj recent...</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderDefaultApp = () => (
    <div className="p-4 text-center">
      <h2 className="text-lg font-bold mb-4">{appContent?.title}</h2>
      <p className="text-sm text-gray-600">Aplicația ta va apărea aici</p>
    </div>
  );

  return (
    <Card className="w-fit mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Smartphone className="h-5 w-5" />
            <span>Preview {platform === 'ios' ? 'iOS' : 'Android'}</span>
          </CardTitle>
          <div className="flex space-x-2">
            <select
              value={selectedDevice}
              onChange={(e) => setSelectedDevice(e.target.value)}
              className="text-sm border rounded px-2 py-1"
            >
              {platform === 'ios' ? (
                <>
                  <option value="iphone-14">iPhone 14</option>
                  <option value="ipad">iPad</option>
                </>
              ) : (
                <>
                  <option value="pixel-7">Pixel 7</option>
                </>
              )}
            </select>
            <Button variant="outline" size="sm">
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex justify-center">
        <div className="relative">
          {/* Device Frame */}
          <div
            className="bg-black p-2 shadow-2xl"
            style={{
              width: device.width + 16,
              height: device.height + 16,
              borderRadius: device.borderRadius
            }}
          >
            {/* Screen */}
            <div
              className="bg-white dark:bg-gray-900 overflow-hidden flex flex-col"
              style={{
                width: device.width,
                height: device.height,
                borderRadius: `calc(${device.borderRadius} - 8px)`
              }}
            >
              {/* Status Bar */}
              <div
                className="bg-gray-100 dark:bg-gray-800 border-b"
                style={{ height: device.statusBarHeight }}
              >
                {renderStatusBar()}
              </div>

              {/* App Content */}
              <div className="flex-1 overflow-hidden">
                {renderAppContent()}
              </div>

              {/* Navigation Bar (Android) or Home Indicator (iOS) */}
              {platform === 'android' ? (
                renderNavigationBar()
              ) : (
                <div
                  className="bg-gray-100 dark:bg-gray-800 flex items-center justify-center"
                  style={{ height: device.homeIndicatorHeight }}
                >
                  <div className="w-32 h-1 bg-gray-400 rounded-full"></div>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
