"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, Play, Pause, Download, Settings, Smartphone, Code, MessageSquare } from "lucide-react";
import Link from "next/link";
import { CodeEditor } from "@/components/code-editor/CodeEditor";
import { MobileSimulator } from "@/components/mobile-preview/MobileSimulator";
import { ChatInterface } from "@/components/chat/ChatInterface";
import { CodeFile, ChatMessage, Project, WorkspaceState } from "@/types";
import { aiService } from "@/lib/ai/aiService";
import { useWebSocket } from "@/lib/websocket/websocketService";

export default function WorkspacePage() {
  const [workspaceState, setWorkspaceState] = useState<WorkspaceState>({
    previewPlatform: 'android',
    isBuilding: false,
    buildLogs: [],
    chatMessages: []
  });

  const [files, setFiles] = useState<CodeFile[]>([
    {
      id: '1',
      name: 'App.tsx',
      path: '/src/App.tsx',
      content: `import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';

const ShoppingApp = () => {
  const [products, setProducts] = useState([
    { id: 1, name: 'Telefon Samsung', price: 2500, image: 'phone.jpg' },
    { id: 2, name: 'Laptop Dell', price: 3500, image: 'laptop.jpg' },
    { id: 3, name: 'Căști Sony', price: 450, image: 'headphones.jpg' }
  ]);

  const [cart, setCart] = useState([]);

  const addToCart = (product) => {
    setCart([...cart, product]);
  };

  const renderProduct = ({ item }) => (
    <View style={styles.productCard}>
      <Text style={styles.productName}>{item.name}</Text>
      <Text style={styles.productPrice}>{item.price} RON</Text>
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => addToCart(item)}
      >
        <Text style={styles.buttonText}>Adaugă în coș</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Magazin Online</Text>
      <FlatList
        data={products}
        renderItem={renderProduct}
        keyExtractor={item => item.id.toString()}
        style={styles.productList}
      />
      <View style={styles.cartInfo}>
        <Text>Produse în coș: {cart.length}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5'
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20
  },
  productCard: {
    backgroundColor: 'white',
    padding: 15,
    marginBottom: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  productName: {
    fontSize: 18,
    fontWeight: 'bold'
  },
  productPrice: {
    fontSize: 16,
    color: '#666',
    marginVertical: 5
  },
  addButton: {
    backgroundColor: '#007bff',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center'
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold'
  },
  productList: {
    flex: 1
  },
  cartInfo: {
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 8,
    marginTop: 10
  }
});

export default ShoppingApp;`,
      language: 'typescript',
      lastModified: new Date()
    }
  ]);

  const [activeFileId, setActiveFileId] = useState<string>('1');
  const { startBuild, onBuildStatus, onBuildLog } = useWebSocket();

  useEffect(() => {
    // Setup WebSocket listeners for build events
    onBuildStatus((status, data) => {
      setWorkspaceState(prev => ({
        ...prev,
        isBuilding: status === 'started' || status === 'progress'
      }));
    });

    onBuildLog((log) => {
      setWorkspaceState(prev => ({
        ...prev,
        buildLogs: [...prev.buildLogs, log]
      }));
    });
  }, []);

  const buildApp = () => {
    setWorkspaceState(prev => ({ ...prev, isBuilding: true }));
    startBuild('current-project', workspaceState.previewPlatform);

    // Fallback timeout in case WebSocket doesn't respond
    setTimeout(() => {
      setWorkspaceState(prev => ({ ...prev, isBuilding: false }));
    }, 5000);
  };

  const handleFileChange = (fileId: string, content: string) => {
    setFiles(prev => prev.map(file =>
      file.id === fileId
        ? { ...file, content, lastModified: new Date() }
        : file
    ));
  };

  const handleFileCreate = (name: string, language: CodeFile['language']) => {
    const newFile: CodeFile = {
      id: Date.now().toString(),
      name,
      path: `/src/${name}`,
      content: getTemplateForLanguage(language),
      language,
      lastModified: new Date()
    };
    setFiles(prev => [...prev, newFile]);
    setActiveFileId(newFile.id);
  };

  const handleFileDelete = (fileId: string) => {
    if (files.length <= 1) return; // Don't delete the last file

    setFiles(prev => prev.filter(file => file.id !== fileId));
    if (activeFileId === fileId) {
      setActiveFileId(files.find(f => f.id !== fileId)?.id || '');
    }
  };

  const handleChatMessage = async (content: string, attachments?: File[]) => {
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content,
      timestamp: new Date(),
      attachments
    };

    setWorkspaceState(prev => ({
      ...prev,
      chatMessages: [...prev.chatMessages, userMessage]
    }));

    // Process AI request with current code context
    try {
      const activeFile = files.find(f => f.id === activeFileId);
      const response = await aiService.sendMessage({
        message: content,
        context: {
          currentCode: activeFile?.content,
          platform: workspaceState.previewPlatform
        }
      });

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: response.message,
        timestamp: new Date(),
        metadata: {
          confidence: response.confidence
        }
      };

      setWorkspaceState(prev => ({
        ...prev,
        chatMessages: [...prev.chatMessages, aiMessage]
      }));

      // If AI suggests code changes, apply them
      if (response.codeChanges && response.codeChanges.length > 0) {
        response.codeChanges.forEach(change => {
          if (change.action === 'create') {
            handleFileCreate(change.file, 'typescript');
          } else if (change.action === 'update' && change.content) {
            const targetFile = files.find(f => f.name === change.file);
            if (targetFile) {
              handleFileChange(targetFile.id, change.content);
            }
          }
        });
      }
    } catch (error) {
      console.error('Error processing chat message:', error);
    }
  };

  const getTemplateForLanguage = (language: CodeFile['language']): string => {
    switch (language) {
      case 'typescript':
        return `import React from 'react';

const NewComponent = () => {
  return (
    <div>
      <h1>New Component</h1>
    </div>
  );
};

export default NewComponent;`;
      case 'javascript':
        return `import React from 'react';

const NewComponent = () => {
  return (
    <div>
      <h1>New Component</h1>
    </div>
  );
};

export default NewComponent;`;
      case 'kotlin':
        return `package com.example.myapp

import android.os.Bundle
import androidx.activity.ComponentActivity

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // Your code here
    }
}`;
      case 'swift':
        return `import UIKit

class ViewController: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        // Your code here
    }
}`;
      case 'dart':
        return `import 'package:flutter/material.dart';

class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('My App')),
      body: Center(child: Text('Hello World')),
    );
  }
}`;
      default:
        return '';
    }
  };

  const activeFile = files.find(f => f.id === activeFileId);
  const appCode = activeFile?.content || '';

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Link href="/">
              <Button variant="ghost" size="sm" className="mr-4">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Înapoi
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold">Workspace Dezvoltare</h1>
              <p className="text-gray-600 dark:text-gray-300">
                {activeFile?.name || 'Aplicație'} - React Native
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={buildApp} disabled={workspaceState.isBuilding}>
              {workspaceState.isBuilding ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
              {workspaceState.isBuilding ? 'Building...' : 'Build App'}
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Workspace Grid */}
        <div className="grid grid-cols-12 gap-6 h-[calc(100vh-200px)]">
          {/* Chat Panel */}
          <div className="col-span-3">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Chat Rapid
                </CardTitle>
              </CardHeader>
              <CardContent className="h-full p-0">
                <ChatInterface
                  messages={workspaceState.chatMessages}
                  onSendMessage={handleChatMessage}
                  isAiTyping={false}
                  className="h-full"
                />
              </CardContent>
            </Card>
          </div>

          {/* Code Editor */}
          <div className="col-span-5">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Code className="h-5 w-5 mr-2" />
                  Editor Cod
                </CardTitle>
              </CardHeader>
              <CardContent className="h-full p-0">
                <CodeEditor
                  files={files}
                  activeFileId={activeFileId}
                  onFileChange={handleFileChange}
                  onFileSelect={setActiveFileId}
                  onFileCreate={handleFileCreate}
                  onFileDelete={handleFileDelete}
                  className="h-full"
                />
              </CardContent>
            </Card>
          </div>

          {/* Mobile Preview */}
          <div className="col-span-4">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Smartphone className="h-5 w-5 mr-2" />
                    Preview Aplicație
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant={workspaceState.previewPlatform === 'android' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setWorkspaceState(prev => ({ ...prev, previewPlatform: 'android' }))}
                    >
                      Android
                    </Button>
                    <Button
                      variant={workspaceState.previewPlatform === 'ios' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setWorkspaceState(prev => ({ ...prev, previewPlatform: 'ios' }))}
                    >
                      iOS
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="flex justify-center items-start pt-4">
                <MobileSimulator
                  platform={workspaceState.previewPlatform}
                  appCode={appCode}
                  onInteraction={(event) => {
                    console.log('Simulator interaction:', event);
                  }}
                />

                {/* Build Status Overlay */}
                {workspaceState.isBuilding && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                    <div className="bg-white p-4 rounded-lg text-center">
                      <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
                      <p className="text-sm">Building aplicația...</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Bottom Panel */}
        <div className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Progres Dezvoltare</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">✓</div>
                  <p className="text-sm">Structura de bază</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">✓</div>
                  <p className="text-sm">Componente UI</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">⚡</div>
                  <p className="text-sm">Funcționalități</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-400">○</div>
                  <p className="text-sm">Testing</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
