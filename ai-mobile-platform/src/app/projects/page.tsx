"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Plus, Search, Filter, MoreVertical, Play, Download, Trash2 } from "lucide-react";
import Link from "next/link";

interface Project {
  id: string;
  name: string;
  description: string;
  platform: 'React Native' | 'Flutter' | 'Android' | 'iOS';
  status: 'În dezvoltare' | 'Completat' | 'Pauzat';
  progress: number;
  lastModified: string;
  thumbnail: string;
}

export default function ProjectsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPlatform, setFilterPlatform] = useState<string>('all');

  const projects: Project[] = [
    {
      id: '1',
      name: 'Magazin Online',
      description: 'Aplicație e-commerce cu plăți integrate și management produse',
      platform: 'React Native',
      status: 'În dezvoltare',
      progress: 75,
      lastModified: 'Acum 2 ore',
      thumbnail: '🛒'
    },
    {
      id: '2',
      name: 'Fitness Tracker',
      description: 'App pentru tracking antrenamente și progres fitness',
      platform: 'Flutter',
      status: 'Completat',
      progress: 100,
      lastModified: 'Ieri',
      thumbnail: '💪'
    },
    {
      id: '3',
      name: 'Chat Social',
      description: 'Aplicație de mesagerie cu funcții sociale',
      platform: 'React Native',
      status: 'Pauzat',
      progress: 45,
      lastModified: 'Acum 3 zile',
      thumbnail: '💬'
    },
    {
      id: '4',
      name: 'Task Manager',
      description: 'Organizator de sarcini cu sincronizare cloud',
      platform: 'Flutter',
      status: 'În dezvoltare',
      progress: 60,
      lastModified: 'Acum 1 zi',
      thumbnail: '✅'
    },
    {
      id: '5',
      name: 'Weather App',
      description: 'Aplicație meteo cu predicții și alerte',
      platform: 'Android',
      status: 'Completat',
      progress: 100,
      lastModified: 'Săptămâna trecută',
      thumbnail: '🌤️'
    },
    {
      id: '6',
      name: 'Recipe Book',
      description: 'Colecție de rețete cu funcție de căutare',
      platform: 'iOS',
      status: 'În dezvoltare',
      progress: 30,
      lastModified: 'Acum 4 ore',
      thumbnail: '👨‍🍳'
    }
  ];

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesPlatform = filterPlatform === 'all' || project.platform === filterPlatform;
    return matchesSearch && matchesPlatform;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completat': return 'text-green-600 bg-green-100';
      case 'În dezvoltare': return 'text-blue-600 bg-blue-100';
      case 'Pauzat': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'React Native': return 'text-blue-600 bg-blue-100';
      case 'Flutter': return 'text-cyan-600 bg-cyan-100';
      case 'Android': return 'text-green-600 bg-green-100';
      case 'iOS': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Link href="/">
              <Button variant="ghost" size="sm" className="mr-4">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Înapoi
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold">Proiectele Mele</h1>
              <p className="text-gray-600 dark:text-gray-300">Gestionează și monitorizează aplicațiile tale</p>
            </div>
          </div>
          <Link href="/chat">
            <Button className="flex items-center">
              <Plus className="h-4 w-4 mr-2" />
              Proiect Nou
            </Button>
          </Link>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Caută proiecte..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-600"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <select
              value={filterPlatform}
              onChange={(e) => setFilterPlatform(e.target.value)}
              className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-600"
            >
              <option value="all">Toate platformele</option>
              <option value="React Native">React Native</option>
              <option value="Flutter">Flutter</option>
              <option value="Android">Android</option>
              <option value="iOS">iOS</option>
            </select>
          </div>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => (
            <Card key={project.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="text-3xl">{project.thumbnail}</div>
                    <div>
                      <CardTitle className="text-lg">{project.name}</CardTitle>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                        {project.description}
                      </p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Platform and Status */}
                <div className="flex items-center justify-between">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPlatformColor(project.platform)}`}>
                    {project.platform}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                    {project.status}
                  </span>
                </div>

                {/* Progress */}
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Progres</span>
                    <span>{project.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{width: `${project.progress}%`}}
                    ></div>
                  </div>
                </div>

                {/* Last Modified */}
                <p className="text-xs text-gray-500">
                  Ultima modificare: {project.lastModified}
                </p>

                {/* Actions */}
                <div className="flex space-x-2 pt-2">
                  <Link href="/workspace" className="flex-1">
                    <Button variant="outline" size="sm" className="w-full">
                      <Play className="h-4 w-4 mr-2" />
                      Deschide
                    </Button>
                  </Link>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredProjects.length === 0 && (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">📱</div>
            <h3 className="text-xl font-semibold mb-2">Nu s-au găsit proiecte</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              {searchTerm || filterPlatform !== 'all' 
                ? 'Încearcă să modifici criteriile de căutare'
                : 'Începe primul tău proiect cu MobileGenius AI'
              }
            </p>
            <Link href="/chat">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Creează Primul Proiect
              </Button>
            </Link>
          </div>
        )}

        {/* Stats Summary */}
        {filteredProjects.length > 0 && (
          <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{projects.length}</div>
                <div className="text-sm text-gray-600">Total Proiecte</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">
                  {projects.filter(p => p.status === 'Completat').length}
                </div>
                <div className="text-sm text-gray-600">Completate</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {projects.filter(p => p.status === 'În dezvoltare').length}
                </div>
                <div className="text-sm text-gray-600">În Dezvoltare</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {Math.round(projects.reduce((acc, p) => acc + p.progress, 0) / projects.length)}%
                </div>
                <div className="text-sm text-gray-600">Progres Mediu</div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
