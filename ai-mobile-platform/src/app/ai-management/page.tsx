"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Brain, TrendingUp, Settings, Download, Upload, BarChart3, Zap } from "lucide-react";
import Link from "next/link";

export default function AIManagementPage() {
  const [selectedTab, setSelectedTab] = useState<'stats' | 'training' | 'config'>('stats');

  const aiStats = {
    totalProjects: 47,
    successRate: 94.2,
    learningProgress: 78,
    responseTime: 1.2,
    knowledgeBase: 15420,
    lastUpdate: "2 ore în urmă"
  };

  const recentLearning = [
    { topic: "React Native Navigation", confidence: 95, date: "Azi" },
    { topic: "Flutter State Management", confidence: 88, date: "Ieri" },
    { topic: "iOS Swift UI", confidence: 92, date: "Acum 2 zile" },
    { topic: "Android Jetpack Compose", confidence: 89, date: "Acum 3 zile" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-indigo-50 to-blue-50 dark:from-gray-900 dark:via-purple-900 dark:to-indigo-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Link href="/">
              <Button variant="ghost" size="sm" className="mr-4">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Înapoi
              </Button>
            </Link>
            <div className="flex items-center">
              <Brain className="h-8 w-8 text-purple-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold">Management AI</h1>
                <p className="text-gray-600 dark:text-gray-300">Monitorizează și optimizează MobileGenius AI</p>
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Date
            </Button>
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import Model
            </Button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 mb-8 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg w-fit">
          <Button
            variant={selectedTab === 'stats' ? 'default' : 'ghost'}
            onClick={() => setSelectedTab('stats')}
            className="flex items-center"
          >
            <BarChart3 className="h-4 w-4 mr-2" />
            Statistici
          </Button>
          <Button
            variant={selectedTab === 'training' ? 'default' : 'ghost'}
            onClick={() => setSelectedTab('training')}
            className="flex items-center"
          >
            <TrendingUp className="h-4 w-4 mr-2" />
            Antrenament
          </Button>
          <Button
            variant={selectedTab === 'config' ? 'default' : 'ghost'}
            onClick={() => setSelectedTab('config')}
            className="flex items-center"
          >
            <Settings className="h-4 w-4 mr-2" />
            Configurare
          </Button>
        </div>

        {/* Content based on selected tab */}
        {selectedTab === 'stats' && (
          <div className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Proiecte Totale</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{aiStats.totalProjects}</div>
                  <p className="text-xs text-green-600">+12% față de luna trecută</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Rata de Succes</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{aiStats.successRate}%</div>
                  <p className="text-xs text-green-600">+2.1% îmbunătățire</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Progres Învățare</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{aiStats.learningProgress}%</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div 
                      className="bg-purple-600 h-2 rounded-full" 
                      style={{width: `${aiStats.learningProgress}%`}}
                    ></div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Timp Răspuns</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{aiStats.responseTime}s</div>
                  <p className="text-xs text-green-600">-0.3s optimizare</p>
                </CardContent>
              </Card>
            </div>

            {/* Performance Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Performanță în Timp</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gray-50 dark:bg-gray-800 rounded flex items-center justify-center">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Grafic de performanță va fi implementat aici</p>
                    <p className="text-sm text-gray-400">Integrare cu biblioteci de charting</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Learning */}
            <Card>
              <CardHeader>
                <CardTitle>Învățare Recentă</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentLearning.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded">
                      <div>
                        <h4 className="font-medium">{item.topic}</h4>
                        <p className="text-sm text-gray-600">{item.date}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-green-600">{item.confidence}%</div>
                        <div className="text-xs text-gray-500">Încredere</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {selectedTab === 'training' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Zap className="h-5 w-5 mr-2" />
                  Antrenament Activ
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                    <h3 className="font-semibold mb-2">Sesiune Curentă: Flutter Advanced Patterns</h3>
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-blue-600 h-2 rounded-full" style={{width: '65%'}}></div>
                        </div>
                      </div>
                      <span className="ml-4 text-sm font-medium">65%</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">Estimare finalizare: 2 ore</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Button className="h-20 flex flex-col">
                      <span className="font-semibold">Antrenament Manual</span>
                      <span className="text-sm opacity-80">Încarcă exemple noi</span>
                    </Button>
                    <Button variant="outline" className="h-20 flex flex-col">
                      <span className="font-semibold">Auto-Learning</span>
                      <span className="text-sm opacity-80">Activează învățarea automată</span>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Domenii de Specializare</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    { name: "React Native", level: 95, color: "bg-blue-500" },
                    { name: "Flutter", level: 88, color: "bg-cyan-500" },
                    { name: "iOS Native", level: 82, color: "bg-gray-500" },
                    { name: "Android Native", level: 90, color: "bg-green-500" }
                  ].map((skill, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium">{skill.name}</span>
                        <span className="text-sm font-bold">{skill.level}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`${skill.color} h-2 rounded-full`}
                          style={{width: `${skill.level}%`}}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {selectedTab === 'config' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Configurări Generale</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">Creativitate AI</label>
                    <input 
                      type="range" 
                      min="0" 
                      max="100" 
                      defaultValue="75"
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>Conservativ</span>
                      <span>Creativ</span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Viteza de Răspuns</label>
                    <select className="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-purple-500">
                      <option>Rapidă (mai puțin detaliat)</option>
                      <option selected>Echilibrată</option>
                      <option>Detaliată (mai lentă)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Limbaj Preferat</label>
                    <select className="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-purple-500">
                      <option selected>Română</option>
                      <option>English</option>
                      <option>Ambele</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Framework Preferat</label>
                    <select className="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-purple-500">
                      <option>React Native</option>
                      <option>Flutter</option>
                      <option>Native (Android/iOS)</option>
                      <option selected>Auto-detect</option>
                    </select>
                  </div>
                </div>

                <div className="border-t pt-6">
                  <h3 className="font-semibold mb-4">Preferințe Avansate</h3>
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" defaultChecked />
                      <span>Activează învățarea din feedback</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" defaultChecked />
                      <span>Sugestii proactive pentru optimizare</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" />
                      <span>Mod debug pentru dezvoltatori</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" defaultChecked />
                      <span>Auto-save progres proiecte</span>
                    </label>
                  </div>
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <Button variant="outline">Resetează</Button>
                  <Button>Salvează Configurările</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
