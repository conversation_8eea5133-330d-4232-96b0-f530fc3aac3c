"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowL<PERSON><PERSON>, <PERSON> } from "lucide-react";
import Link from "next/link";
import { ChatInterface } from "@/components/chat/ChatInterface";
import { SuggestionsPanel } from "@/components/chat/SuggestionsPanel";
import { ChatMessage } from "@/types";
import { aiService } from "@/lib/ai/aiService";
import { useWebSocket } from "@/lib/websocket/websocketService";

export default function ChatPage() {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Salut! Sunt MobileGenius AI, asistentul tău pentru dezvoltarea aplicațiilor mobile. Spune-mi ce aplicație vrei să creăm împreună! Poți să-mi descrii funcționalitățile, designul, sau orice altă idee ai.',
      timestamp: new Date(),
      metadata: {
        confidence: 1.0,
        model: 'MobileGenius-v1'
      }
    }
  ]);
  const [isAiTyping, setIsAiTyping] = useState(false);

  const {
    connect,
    sendChatMessage,
    onChatMessage,
    onAITyping,
    isConnected
  } = useWebSocket();

  useEffect(() => {
    // Connect to WebSocket
    connect();

    // Setup WebSocket listeners
    onChatMessage((message: ChatMessage) => {
      setMessages(prev => [...prev, message]);
      setIsAiTyping(false);
    });

    onAITyping((typing: boolean) => {
      setIsAiTyping(typing);
    });

    return () => {
      // Cleanup listeners when component unmounts
    };
  }, []);

  const handleSendMessage = async (content: string, attachments?: File[]) => {
    if (!content.trim() && (!attachments || attachments.length === 0)) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content,
      timestamp: new Date(),
      attachments
    };

    setMessages(prev => [...prev, userMessage]);
    setIsAiTyping(true);

    // Send via WebSocket if connected, otherwise use direct AI service
    if (isConnected()) {
      sendChatMessage(userMessage);
    } else {
      // Fallback to direct AI service call
      try {
        const response = await aiService.sendMessage({
          message: content,
          context: {
            // Add any relevant context
          }
        });

        const aiMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: response.message,
          timestamp: new Date(),
          metadata: {
            confidence: response.confidence,
            model: 'MobileGenius-v1'
          }
        };

        setMessages(prev => [...prev, aiMessage]);
      } catch (error) {
        console.error('Error sending message:', error);

        // Show error message
        const errorMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: 'Ne pare rău, a apărut o eroare. Te rog încearcă din nou.',
          timestamp: new Date(),
          metadata: {
            confidence: 0,
            model: 'error'
          }
        };

        setMessages(prev => [...prev, errorMessage]);
      } finally {
        setIsAiTyping(false);
      }
    }
  };

  const handleSuggestionClick = (prompt: string) => {
    handleSendMessage(prompt);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Link href="/">
            <Button variant="ghost" size="sm" className="mr-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Înapoi
            </Button>
          </Link>
          <div className="flex items-center">
            <Brain className="h-8 w-8 text-blue-600 mr-3" />
            <div>
              <h1 className="text-2xl font-bold">Conversație cu MobileGenius AI</h1>
              <p className="text-gray-600 dark:text-gray-300">
                Descrie-mi aplicația pe care vrei să o creezi
                {isConnected() && (
                  <span className="ml-2 text-green-600 text-sm">● Online</span>
                )}
              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {/* Chat Interface */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg h-[700px]">
              <ChatInterface
                messages={messages}
                onSendMessage={handleSendMessage}
                isAiTyping={isAiTyping}
                className="h-full"
              />
            </div>
          </div>

          {/* Suggestions Panel */}
          <div className="lg:col-span-1">
            <SuggestionsPanel
              onSuggestionClick={handleSuggestionClick}
              className="h-fit"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
