{"name": "mobile-ai-platform-backend", "version": "1.0.0", "description": "Backend server for Mobile AI Platform - AI-powered mobile app development", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1", "build": "echo \"No build step required for Node.js\"", "lint": "echo \"<PERSON><PERSON> not configured yet\"", "logs": "tail -f logs/app.log"}, "keywords": ["mobile", "ai", "react-native", "flutter", "app-development", "websocket", "api"], "author": "Mobile AI Platform Team", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "helmet": "^8.1.0", "morgan": "^1.10.0", "socket.io": "^4.8.1"}, "devDependencies": {"concurrently": "^9.2.0", "nodemon": "^3.1.10"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}