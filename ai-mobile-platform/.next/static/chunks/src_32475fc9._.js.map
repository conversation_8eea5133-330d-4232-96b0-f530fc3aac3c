{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/proiecte/webappandroid/ai-mobile-platform/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/proiecte/webappandroid/ai-mobile-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/proiecte/webappandroid/ai-mobile-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/proiecte/webappandroid/ai-mobile-platform/src/components/code-editor/CodeEditor.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef, useState } from \"react\";\nimport { Editor } from \"@monaco-editor/react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { \n  Play, \n  Save, \n  Download, \n  Copy, \n  RotateCcw, \n  Settings, \n  FileText,\n  Folder,\n  Plus,\n  X\n} from \"lucide-react\";\nimport { CodeFile } from \"@/types\";\n\ninterface CodeEditorProps {\n  files: CodeFile[];\n  activeFileId?: string;\n  onFileChange: (fileId: string, content: string) => void;\n  onFileSelect: (fileId: string) => void;\n  onFileCreate: (name: string, language: string) => void;\n  onFileDelete: (fileId: string) => void;\n  readOnly?: boolean;\n  className?: string;\n}\n\nconst languageMap = {\n  typescript: 'typescript',\n  javascript: 'javascript',\n  kotlin: 'kotlin',\n  swift: 'swift',\n  dart: 'dart'\n};\n\nconst fileIcons = {\n  typescript: '📘',\n  javascript: '📙',\n  kotlin: '🟢',\n  swift: '🍎',\n  dart: '🎯'\n};\n\nexport function CodeEditor({\n  files,\n  activeFileId,\n  onFileChange,\n  onFileSelect,\n  onFileCreate,\n  onFileDelete,\n  readOnly = false,\n  className = \"\"\n}: CodeEditorProps) {\n  const [editorTheme, setEditorTheme] = useState<'vs-dark' | 'light'>('vs-dark');\n  const [showFileDialog, setShowFileDialog] = useState(false);\n  const [newFileName, setNewFileName] = useState('');\n  const [newFileLanguage, setNewFileLanguage] = useState<CodeFile['language']>('typescript');\n  const editorRef = useRef<any>(null);\n\n  const activeFile = files.find(f => f.id === activeFileId) || files[0];\n\n  const handleEditorDidMount = (editor: any, monaco: any) => {\n    editorRef.current = editor;\n    \n    // Configure editor options\n    editor.updateOptions({\n      fontSize: 14,\n      lineHeight: 20,\n      minimap: { enabled: true },\n      scrollBeyondLastLine: false,\n      wordWrap: 'on',\n      automaticLayout: true,\n    });\n\n    // Add custom themes\n    monaco.editor.defineTheme('custom-dark', {\n      base: 'vs-dark',\n      inherit: true,\n      rules: [],\n      colors: {\n        'editor.background': '#1e1e1e',\n        'editor.foreground': '#d4d4d4',\n        'editorLineNumber.foreground': '#858585',\n        'editor.selectionBackground': '#264f78',\n        'editor.inactiveSelectionBackground': '#3a3d41',\n      }\n    });\n  };\n\n  const handleCreateFile = () => {\n    if (newFileName.trim()) {\n      onFileCreate(newFileName.trim(), newFileLanguage);\n      setNewFileName('');\n      setShowFileDialog(false);\n    }\n  };\n\n  const copyToClipboard = () => {\n    if (activeFile) {\n      navigator.clipboard.writeText(activeFile.content);\n    }\n  };\n\n  const downloadFile = () => {\n    if (activeFile) {\n      const blob = new Blob([activeFile.content], { type: 'text/plain' });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = activeFile.name;\n      a.click();\n      URL.revokeObjectURL(url);\n    }\n  };\n\n  const formatCode = () => {\n    if (editorRef.current) {\n      editorRef.current.getAction('editor.action.formatDocument').run();\n    }\n  };\n\n  return (\n    <div className={`flex flex-col h-full ${className}`}>\n      {/* File Tabs */}\n      <div className=\"flex items-center justify-between border-b bg-gray-50 dark:bg-gray-800 p-2\">\n        <div className=\"flex items-center space-x-1 overflow-x-auto\">\n          {files.map((file) => (\n            <div\n              key={file.id}\n              className={`flex items-center space-x-2 px-3 py-1 rounded cursor-pointer text-sm ${\n                file.id === activeFileId\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600'\n              }`}\n              onClick={() => onFileSelect(file.id)}\n            >\n              <span>{fileIcons[file.language]}</span>\n              <span>{file.name}</span>\n              {files.length > 1 && (\n                <button\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    onFileDelete(file.id);\n                  }}\n                  className=\"hover:bg-red-500 rounded p-1\"\n                >\n                  <X className=\"h-3 w-3\" />\n                </button>\n              )}\n            </div>\n          ))}\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => setShowFileDialog(true)}\n            className=\"h-8 w-8 p-0\"\n          >\n            <Plus className=\"h-4 w-4\" />\n          </Button>\n        </div>\n\n        {/* Editor Controls */}\n        <div className=\"flex items-center space-x-1\">\n          <Button variant=\"ghost\" size=\"sm\" onClick={copyToClipboard}>\n            <Copy className=\"h-4 w-4\" />\n          </Button>\n          <Button variant=\"ghost\" size=\"sm\" onClick={downloadFile}>\n            <Download className=\"h-4 w-4\" />\n          </Button>\n          <Button variant=\"ghost\" size=\"sm\" onClick={formatCode}>\n            <RotateCcw className=\"h-4 w-4\" />\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => setEditorTheme(editorTheme === 'vs-dark' ? 'light' : 'vs-dark')}\n          >\n            <Settings className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n\n      {/* Editor */}\n      <div className=\"flex-1\">\n        {activeFile ? (\n          <Editor\n            height=\"100%\"\n            language={languageMap[activeFile.language]}\n            value={activeFile.content}\n            theme={editorTheme}\n            onChange={(value) => onFileChange(activeFile.id, value || '')}\n            onMount={handleEditorDidMount}\n            options={{\n              readOnly,\n              minimap: { enabled: true },\n              fontSize: 14,\n              lineHeight: 20,\n              scrollBeyondLastLine: false,\n              wordWrap: 'on',\n              automaticLayout: true,\n              suggestOnTriggerCharacters: true,\n              quickSuggestions: true,\n              parameterHints: { enabled: true },\n              formatOnPaste: true,\n              formatOnType: true,\n            }}\n          />\n        ) : (\n          <div className=\"flex items-center justify-center h-full text-gray-500\">\n            <div className=\"text-center\">\n              <FileText className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n              <p>Nu există fișiere deschise</p>\n              <Button\n                variant=\"outline\"\n                className=\"mt-4\"\n                onClick={() => setShowFileDialog(true)}\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Creează primul fișier\n              </Button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* File Creation Dialog */}\n      {showFileDialog && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <Card className=\"w-96\">\n            <CardHeader>\n              <CardTitle>Creează Fișier Nou</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium mb-2\">Nume fișier</label>\n                <input\n                  type=\"text\"\n                  value={newFileName}\n                  onChange={(e) => setNewFileName(e.target.value)}\n                  placeholder=\"ex: MainActivity.kt\"\n                  className=\"w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  autoFocus\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium mb-2\">Limbaj</label>\n                <select\n                  value={newFileLanguage}\n                  onChange={(e) => setNewFileLanguage(e.target.value as CodeFile['language'])}\n                  className=\"w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"typescript\">TypeScript</option>\n                  <option value=\"javascript\">JavaScript</option>\n                  <option value=\"kotlin\">Kotlin</option>\n                  <option value=\"swift\">Swift</option>\n                  <option value=\"dart\">Dart</option>\n                </select>\n              </div>\n              <div className=\"flex space-x-2 pt-4\">\n                <Button onClick={handleCreateFile} className=\"flex-1\">\n                  Creează\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  onClick={() => setShowFileDialog(false)}\n                  className=\"flex-1\"\n                >\n                  Anulează\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Status Bar */}\n      <div className=\"border-t bg-gray-50 dark:bg-gray-800 px-4 py-2 text-xs text-gray-600 dark:text-gray-300 flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          {activeFile && (\n            <>\n              <span>📄 {activeFile.name}</span>\n              <span>🔤 {activeFile.language}</span>\n              <span>📏 {activeFile.content.split('\\n').length} linii</span>\n              <span>📊 {activeFile.content.length} caractere</span>\n            </>\n          )}\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <span>Ultima modificare: {activeFile?.lastModified.toLocaleTimeString()}</span>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AA+BA,MAAM,cAAc;IAClB,YAAY;IACZ,YAAY;IACZ,QAAQ;IACR,OAAO;IACP,MAAM;AACR;AAEA,MAAM,YAAY;IAChB,YAAY;IACZ,YAAY;IACZ,QAAQ;IACR,OAAO;IACP,MAAM;AACR;AAEO,SAAS,WAAW,EACzB,KAAK,EACL,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,WAAW,KAAK,EAChB,YAAY,EAAE,EACE;;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAC7E,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAO;IAE9B,MAAM,aAAa,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB,KAAK,CAAC,EAAE;IAErE,MAAM,uBAAuB,CAAC,QAAa;QACzC,UAAU,OAAO,GAAG;QAEpB,2BAA2B;QAC3B,OAAO,aAAa,CAAC;YACnB,UAAU;YACV,YAAY;YACZ,SAAS;gBAAE,SAAS;YAAK;YACzB,sBAAsB;YACtB,UAAU;YACV,iBAAiB;QACnB;QAEA,oBAAoB;QACpB,OAAO,MAAM,CAAC,WAAW,CAAC,eAAe;YACvC,MAAM;YACN,SAAS;YACT,OAAO,EAAE;YACT,QAAQ;gBACN,qBAAqB;gBACrB,qBAAqB;gBACrB,+BAA+B;gBAC/B,8BAA8B;gBAC9B,sCAAsC;YACxC;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,YAAY,IAAI,IAAI;YACtB,aAAa,YAAY,IAAI,IAAI;YACjC,eAAe;YACf,kBAAkB;QACpB;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,YAAY;YACd,UAAU,SAAS,CAAC,SAAS,CAAC,WAAW,OAAO;QAClD;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,YAAY;YACd,MAAM,OAAO,IAAI,KAAK;gBAAC,WAAW,OAAO;aAAC,EAAE;gBAAE,MAAM;YAAa;YACjE,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,WAAW,IAAI;YAC5B,EAAE,KAAK;YACP,IAAI,eAAe,CAAC;QACtB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,SAAS,CAAC,gCAAgC,GAAG;QACjE;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,qBAAqB,EAAE,WAAW;;0BAEjD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;oCAEC,WAAW,CAAC,qEAAqE,EAC/E,KAAK,EAAE,KAAK,eACR,2BACA,yEACJ;oCACF,SAAS,IAAM,aAAa,KAAK,EAAE;;sDAEnC,6LAAC;sDAAM,SAAS,CAAC,KAAK,QAAQ,CAAC;;;;;;sDAC/B,6LAAC;sDAAM,KAAK,IAAI;;;;;;wCACf,MAAM,MAAM,GAAG,mBACd,6LAAC;4CACC,SAAS,CAAC;gDACR,EAAE,eAAe;gDACjB,aAAa,KAAK,EAAE;4CACtB;4CACA,WAAU;sDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;mCAlBZ,KAAK,EAAE;;;;;0CAuBhB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,kBAAkB;gCACjC,WAAU;0CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,SAAS;0CACzC,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,SAAS;0CACzC,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,SAAS;0CACzC,cAAA,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe,gBAAgB,YAAY,UAAU;0CAEpE,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM1B,6LAAC;gBAAI,WAAU;0BACZ,2BACC,6LAAC,gLAAA,CAAA,SAAM;oBACL,QAAO;oBACP,UAAU,WAAW,CAAC,WAAW,QAAQ,CAAC;oBAC1C,OAAO,WAAW,OAAO;oBACzB,OAAO;oBACP,UAAU,CAAC,QAAU,aAAa,WAAW,EAAE,EAAE,SAAS;oBAC1D,SAAS;oBACT,SAAS;wBACP;wBACA,SAAS;4BAAE,SAAS;wBAAK;wBACzB,UAAU;wBACV,YAAY;wBACZ,sBAAsB;wBACtB,UAAU;wBACV,iBAAiB;wBACjB,4BAA4B;wBAC5B,kBAAkB;wBAClB,gBAAgB;4BAAE,SAAS;wBAAK;wBAChC,eAAe;wBACf,cAAc;oBAChB;;;;;yCAGF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;0CAAE;;;;;;0CACH,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,kBAAkB;;kDAEjC,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;YAS1C,gCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,6LAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,aAAY;4CACZ,WAAU;4CACV,SAAS;;;;;;;;;;;;8CAGb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CAClD,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,6LAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;;;;;;;8CAGzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAkB,WAAU;sDAAS;;;;;;sDAGtD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,kBAAkB;4CACjC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,4BACC;;8CACE,6LAAC;;wCAAK;wCAAI,WAAW,IAAI;;;;;;;8CACzB,6LAAC;;wCAAK;wCAAI,WAAW,QAAQ;;;;;;;8CAC7B,6LAAC;;wCAAK;wCAAI,WAAW,OAAO,CAAC,KAAK,CAAC,MAAM,MAAM;wCAAC;;;;;;;8CAChD,6LAAC;;wCAAK;wCAAI,WAAW,OAAO,CAAC,MAAM;wCAAC;;;;;;;;;;;;;;kCAI1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;gCAAK;gCAAoB,YAAY,aAAa;;;;;;;;;;;;;;;;;;;;;;;;AAK7D;GA1PgB;KAAA", "debugId": null}}, {"offset": {"line": 796, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/proiecte/webappandroid/ai-mobile-platform/src/components/mobile-preview/MobileSimulator.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { \n  Smartphone, \n  Tablet, \n  RotateCcw, \n  Volume2, \n  Wifi, \n  Battery, \n  Signal,\n  Home,\n  ArrowLeft,\n  MoreVertical,\n  RefreshCw\n} from \"lucide-react\";\nimport { MobileSimulatorProps, SimulatorEvent } from \"@/types\";\n\ninterface DeviceFrame {\n  name: string;\n  width: number;\n  height: number;\n  borderRadius: string;\n  statusBarHeight: number;\n  homeIndicatorHeight: number;\n}\n\nconst devices: Record<string, DeviceFrame> = {\n  'iphone-14': {\n    name: 'iPhone 14',\n    width: 280,\n    height: 600,\n    borderRadius: '2.5rem',\n    statusBarHeight: 44,\n    homeIndicatorHeight: 34\n  },\n  'pixel-7': {\n    name: 'Pixel 7',\n    width: 280,\n    height: 600,\n    borderRadius: '2rem',\n    statusBarHeight: 32,\n    homeIndicatorHeight: 0\n  },\n  'ipad': {\n    name: 'iPad',\n    width: 400,\n    height: 600,\n    borderRadius: '1.5rem',\n    statusBarHeight: 32,\n    homeIndicatorHeight: 20\n  }\n};\n\nexport function MobileSimulator({\n  platform,\n  appCode,\n  deviceType = 'phone',\n  orientation = 'portrait',\n  onInteraction\n}: MobileSimulatorProps) {\n  const [selectedDevice, setSelectedDevice] = useState<string>(\n    platform === 'ios' ? 'iphone-14' : 'pixel-7'\n  );\n  const [isLoading, setIsLoading] = useState(false);\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [appContent, setAppContent] = useState<any>(null);\n\n  const device = devices[selectedDevice];\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  useEffect(() => {\n    // Simulate app compilation and rendering\n    setIsLoading(true);\n    setTimeout(() => {\n      setAppContent(generateAppPreview(appCode, platform));\n      setIsLoading(false);\n    }, 1000);\n  }, [appCode, platform]);\n\n  const generateAppPreview = (code: string, platform: string) => {\n    // This would normally parse the code and generate a preview\n    // For now, we'll return a mock preview based on the code content\n    if (code.includes('ShoppingApp') || code.includes('ecommerce') || code.includes('shopping')) {\n      return {\n        type: 'ecommerce',\n        title: 'Magazin Online',\n        screens: ['ProductList', 'ProductDetail', 'Cart', 'Checkout']\n      };\n    } else if (code.includes('FitnessApp') || code.includes('fitness') || code.includes('workout')) {\n      return {\n        type: 'fitness',\n        title: 'Fitness Tracker',\n        screens: ['Dashboard', 'Workouts', 'Progress', 'Settings']\n      };\n    } else if (code.includes('ChatApp') || code.includes('messaging') || code.includes('chat')) {\n      return {\n        type: 'chat',\n        title: 'Chat Social',\n        screens: ['ChatList', 'ChatDetail', 'Profile', 'Settings']\n      };\n    }\n    \n    return {\n      type: 'default',\n      title: 'Aplicația Mea',\n      screens: ['Home', 'Settings']\n    };\n  };\n\n  const handleDeviceInteraction = (event: React.MouseEvent, elementId: string) => {\n    const rect = event.currentTarget.getBoundingClientRect();\n    const x = event.clientX - rect.left;\n    const y = event.clientY - rect.top;\n\n    const simulatorEvent: SimulatorEvent = {\n      type: 'tap',\n      target: elementId,\n      coordinates: { x, y }\n    };\n\n    onInteraction?.(simulatorEvent);\n  };\n\n  const renderStatusBar = () => (\n    <div className=\"flex items-center justify-between px-4 py-2 text-xs font-medium\">\n      <div className=\"flex items-center space-x-1\">\n        <span>{currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>\n      </div>\n      <div className=\"flex items-center space-x-1\">\n        <Signal className=\"h-3 w-3\" />\n        <Wifi className=\"h-3 w-3\" />\n        <Battery className=\"h-3 w-3\" />\n        <span>100%</span>\n      </div>\n    </div>\n  );\n\n  const renderNavigationBar = () => (\n    <div className=\"flex items-center justify-between px-4 py-3 border-t bg-gray-50 dark:bg-gray-800\">\n      <Button\n        variant=\"ghost\"\n        size=\"sm\"\n        onClick={(e) => handleDeviceInteraction(e, 'nav-back')}\n      >\n        <ArrowLeft className=\"h-4 w-4\" />\n      </Button>\n      <Button\n        variant=\"ghost\"\n        size=\"sm\"\n        onClick={(e) => handleDeviceInteraction(e, 'nav-home')}\n      >\n        <Home className=\"h-4 w-4\" />\n      </Button>\n      <Button\n        variant=\"ghost\"\n        size=\"sm\"\n        onClick={(e) => handleDeviceInteraction(e, 'nav-menu')}\n      >\n        <MoreVertical className=\"h-4 w-4\" />\n      </Button>\n    </div>\n  );\n\n  const renderAppContent = () => {\n    if (isLoading) {\n      return (\n        <div className=\"flex items-center justify-center h-full\">\n          <div className=\"text-center\">\n            <RefreshCw className=\"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600\" />\n            <p className=\"text-sm text-gray-600\">Compilez aplicația...</p>\n          </div>\n        </div>\n      );\n    }\n\n    if (!appContent) {\n      return (\n        <div className=\"flex items-center justify-center h-full\">\n          <div className=\"text-center\">\n            <Smartphone className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\n            <p className=\"text-sm text-gray-600\">Aplicația va apărea aici</p>\n          </div>\n        </div>\n      );\n    }\n\n    // Render different app types\n    switch (appContent.type) {\n      case 'ecommerce':\n        return renderEcommerceApp();\n      case 'fitness':\n        return renderFitnessApp();\n      case 'chat':\n        return renderChatApp();\n      default:\n        return renderDefaultApp();\n    }\n  };\n\n  const renderEcommerceApp = () => (\n    <div className=\"p-4 space-y-4\">\n      <h2 className=\"text-lg font-bold text-center\">Magazin Online</h2>\n      <div className=\"space-y-3\">\n        {['Telefon Samsung', 'Laptop Dell', 'Căști Sony'].map((product, index) => (\n          <div\n            key={index}\n            className=\"bg-gray-50 dark:bg-gray-800 p-3 rounded border cursor-pointer\"\n            onClick={(e) => handleDeviceInteraction(e, `product-${index}`)}\n          >\n            <h3 className=\"font-semibold text-sm\">{product}</h3>\n            <p className=\"text-gray-600 text-xs\">{2500 + index * 500} RON</p>\n            <button className=\"bg-blue-500 text-white px-3 py-1 rounded text-xs mt-2\">\n              Adaugă în coș\n            </button>\n          </div>\n        ))}\n      </div>\n      <div className=\"bg-white dark:bg-gray-700 p-3 rounded border\">\n        <p className=\"text-sm\">Produse în coș: 0</p>\n      </div>\n    </div>\n  );\n\n  const renderFitnessApp = () => (\n    <div className=\"p-4 space-y-4\">\n      <h2 className=\"text-lg font-bold text-center\">Fitness Tracker</h2>\n      <div className=\"grid grid-cols-2 gap-3\">\n        <div className=\"bg-blue-100 p-3 rounded text-center\">\n          <div className=\"text-2xl font-bold\">1,247</div>\n          <div className=\"text-xs\">Pași</div>\n        </div>\n        <div className=\"bg-red-100 p-3 rounded text-center\">\n          <div className=\"text-2xl font-bold\">342</div>\n          <div className=\"text-xs\">Calorii</div>\n        </div>\n        <div className=\"bg-green-100 p-3 rounded text-center\">\n          <div className=\"text-2xl font-bold\">45</div>\n          <div className=\"text-xs\">Min Active</div>\n        </div>\n        <div className=\"bg-purple-100 p-3 rounded text-center\">\n          <div className=\"text-2xl font-bold\">7.2</div>\n          <div className=\"text-xs\">Km</div>\n        </div>\n      </div>\n      <button className=\"w-full bg-blue-600 text-white py-3 rounded font-semibold\">\n        Începe Antrenament\n      </button>\n    </div>\n  );\n\n  const renderChatApp = () => (\n    <div className=\"flex flex-col h-full\">\n      <div className=\"p-4 border-b\">\n        <h2 className=\"text-lg font-bold\">Chat Social</h2>\n      </div>\n      <div className=\"flex-1 p-4 space-y-3\">\n        {['Ana Maria', 'Bogdan', 'Cristina'].map((name, index) => (\n          <div\n            key={index}\n            className=\"flex items-center space-x-3 p-2 hover:bg-gray-100 rounded cursor-pointer\"\n            onClick={(e) => handleDeviceInteraction(e, `chat-${index}`)}\n          >\n            <div className=\"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm\">\n              {name[0]}\n            </div>\n            <div className=\"flex-1\">\n              <div className=\"font-semibold text-sm\">{name}</div>\n              <div className=\"text-xs text-gray-600\">Mesaj recent...</div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  const renderDefaultApp = () => (\n    <div className=\"p-4 text-center\">\n      <h2 className=\"text-lg font-bold mb-4\">{appContent?.title}</h2>\n      <p className=\"text-sm text-gray-600\">Aplicația ta va apărea aici</p>\n    </div>\n  );\n\n  return (\n    <Card className=\"w-fit mx-auto\">\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Smartphone className=\"h-5 w-5\" />\n            <span>Preview {platform === 'ios' ? 'iOS' : 'Android'}</span>\n          </CardTitle>\n          <div className=\"flex space-x-2\">\n            <select\n              value={selectedDevice}\n              onChange={(e) => setSelectedDevice(e.target.value)}\n              className=\"text-sm border rounded px-2 py-1\"\n            >\n              {platform === 'ios' ? (\n                <>\n                  <option value=\"iphone-14\">iPhone 14</option>\n                  <option value=\"ipad\">iPad</option>\n                </>\n              ) : (\n                <>\n                  <option value=\"pixel-7\">Pixel 7</option>\n                </>\n              )}\n            </select>\n            <Button variant=\"outline\" size=\"sm\">\n              <RotateCcw className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </CardHeader>\n      <CardContent className=\"flex justify-center\">\n        <div className=\"relative\">\n          {/* Device Frame */}\n          <div\n            className=\"bg-black p-2 shadow-2xl\"\n            style={{\n              width: device.width + 16,\n              height: device.height + 16,\n              borderRadius: device.borderRadius\n            }}\n          >\n            {/* Screen */}\n            <div\n              className=\"bg-white dark:bg-gray-900 overflow-hidden flex flex-col\"\n              style={{\n                width: device.width,\n                height: device.height,\n                borderRadius: `calc(${device.borderRadius} - 8px)`\n              }}\n            >\n              {/* Status Bar */}\n              <div\n                className=\"bg-gray-100 dark:bg-gray-800 border-b\"\n                style={{ height: device.statusBarHeight }}\n              >\n                {renderStatusBar()}\n              </div>\n\n              {/* App Content */}\n              <div className=\"flex-1 overflow-hidden\">\n                {renderAppContent()}\n              </div>\n\n              {/* Navigation Bar (Android) or Home Indicator (iOS) */}\n              {platform === 'android' ? (\n                renderNavigationBar()\n              ) : (\n                <div\n                  className=\"bg-gray-100 dark:bg-gray-800 flex items-center justify-center\"\n                  style={{ height: device.homeIndicatorHeight }}\n                >\n                  <div className=\"w-32 h-1 bg-gray-400 rounded-full\"></div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AA6BA,MAAM,UAAuC;IAC3C,aAAa;QACX,MAAM;QACN,OAAO;QACP,QAAQ;QACR,cAAc;QACd,iBAAiB;QACjB,qBAAqB;IACvB;IACA,WAAW;QACT,MAAM;QACN,OAAO;QACP,QAAQ;QACR,cAAc;QACd,iBAAiB;QACjB,qBAAqB;IACvB;IACA,QAAQ;QACN,MAAM;QACN,OAAO;QACP,QAAQ;QACR,cAAc;QACd,iBAAiB;QACjB,qBAAqB;IACvB;AACF;AAEO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,OAAO,EACP,aAAa,OAAO,EACpB,cAAc,UAAU,EACxB,aAAa,EACQ;;IACrB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACjD,aAAa,QAAQ,cAAc;IAErC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAElD,MAAM,SAAS,OAAO,CAAC,eAAe;IAEtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,QAAQ;mDAAY;oBACxB,eAAe,IAAI;gBACrB;kDAAG;YACH;6CAAO,IAAM,cAAc;;QAC7B;oCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,yCAAyC;YACzC,aAAa;YACb;6CAAW;oBACT,cAAc,mBAAmB,SAAS;oBAC1C,aAAa;gBACf;4CAAG;QACL;oCAAG;QAAC;QAAS;KAAS;IAEtB,MAAM,qBAAqB,CAAC,MAAc;QACxC,4DAA4D;QAC5D,iEAAiE;QACjE,IAAI,KAAK,QAAQ,CAAC,kBAAkB,KAAK,QAAQ,CAAC,gBAAgB,KAAK,QAAQ,CAAC,aAAa;YAC3F,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,SAAS;oBAAC;oBAAe;oBAAiB;oBAAQ;iBAAW;YAC/D;QACF,OAAO,IAAI,KAAK,QAAQ,CAAC,iBAAiB,KAAK,QAAQ,CAAC,cAAc,KAAK,QAAQ,CAAC,YAAY;YAC9F,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,SAAS;oBAAC;oBAAa;oBAAY;oBAAY;iBAAW;YAC5D;QACF,OAAO,IAAI,KAAK,QAAQ,CAAC,cAAc,KAAK,QAAQ,CAAC,gBAAgB,KAAK,QAAQ,CAAC,SAAS;YAC1F,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,SAAS;oBAAC;oBAAY;oBAAc;oBAAW;iBAAW;YAC5D;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;YACP,SAAS;gBAAC;gBAAQ;aAAW;QAC/B;IACF;IAEA,MAAM,0BAA0B,CAAC,OAAyB;QACxD,MAAM,OAAO,MAAM,aAAa,CAAC,qBAAqB;QACtD,MAAM,IAAI,MAAM,OAAO,GAAG,KAAK,IAAI;QACnC,MAAM,IAAI,MAAM,OAAO,GAAG,KAAK,GAAG;QAElC,MAAM,iBAAiC;YACrC,MAAM;YACN,QAAQ;YACR,aAAa;gBAAE;gBAAG;YAAE;QACtB;QAEA,gBAAgB;IAClB;IAEA,MAAM,kBAAkB,kBACtB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;kCAAM,YAAY,kBAAkB,CAAC,EAAE,EAAE;4BAAE,MAAM;4BAAW,QAAQ;wBAAU;;;;;;;;;;;8BAEjF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;sCAAK;;;;;;;;;;;;;;;;;;IAKZ,MAAM,sBAAsB,kBAC1B,6LAAC;YAAI,WAAU;;8BACb,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,CAAC,IAAM,wBAAwB,GAAG;8BAE3C,cAAA,6LAAC,mNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;8BAEvB,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,CAAC,IAAM,wBAAwB,GAAG;8BAE3C,cAAA,6LAAC,sMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;8BAElB,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,CAAC,IAAM,wBAAwB,GAAG;8BAE3C,cAAA,6LAAC,6NAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;;IAK9B,MAAM,mBAAmB;QACvB,IAAI,WAAW;YACb,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;QAI7C;QAEA,IAAI,CAAC,YAAY;YACf,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,iNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;sCACtB,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;QAI7C;QAEA,6BAA6B;QAC7B,OAAQ,WAAW,IAAI;YACrB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB,kBACzB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAgC;;;;;;8BAC9C,6LAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAmB;wBAAe;qBAAa,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9D,6LAAC;4BAEC,WAAU;4BACV,SAAS,CAAC,IAAM,wBAAwB,GAAG,CAAC,QAAQ,EAAE,OAAO;;8CAE7D,6LAAC;oCAAG,WAAU;8CAAyB;;;;;;8CACvC,6LAAC;oCAAE,WAAU;;wCAAyB,OAAO,QAAQ;wCAAI;;;;;;;8CACzD,6LAAC;oCAAO,WAAU;8CAAwD;;;;;;;2BANrE;;;;;;;;;;8BAYX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAK7B,MAAM,mBAAmB,kBACvB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAgC;;;;;;8BAC9C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAqB;;;;;;8CACpC,6LAAC;oCAAI,WAAU;8CAAU;;;;;;;;;;;;sCAE3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAqB;;;;;;8CACpC,6LAAC;oCAAI,WAAU;8CAAU;;;;;;;;;;;;sCAE3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAqB;;;;;;8CACpC,6LAAC;oCAAI,WAAU;8CAAU;;;;;;;;;;;;sCAE3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAqB;;;;;;8CACpC,6LAAC;oCAAI,WAAU;8CAAU;;;;;;;;;;;;;;;;;;8BAG7B,6LAAC;oBAAO,WAAU;8BAA2D;;;;;;;;;;;;IAMjF,MAAM,gBAAgB,kBACpB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCAAoB;;;;;;;;;;;8BAEpC,6LAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAa;wBAAU;qBAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9C,6LAAC;4BAEC,WAAU;4BACV,SAAS,CAAC,IAAM,wBAAwB,GAAG,CAAC,KAAK,EAAE,OAAO;;8CAE1D,6LAAC;oCAAI,WAAU;8CACZ,IAAI,CAAC,EAAE;;;;;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAyB;;;;;;sDACxC,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;2BATpC;;;;;;;;;;;;;;;;IAiBf,MAAM,mBAAmB,kBACvB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA0B,YAAY;;;;;;8BACpD,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;IAIzC,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,iNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC;;wCAAK;wCAAS,aAAa,QAAQ,QAAQ;;;;;;;;;;;;;sCAE9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oCACjD,WAAU;8CAET,aAAa,sBACZ;;0DACE,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAO;;;;;;;qEAGvB;kDACE,cAAA,6LAAC;4CAAO,OAAM;sDAAU;;;;;;;;;;;;8CAI9B,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAC7B,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK7B,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;8BAEb,cAAA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO,OAAO,KAAK,GAAG;4BACtB,QAAQ,OAAO,MAAM,GAAG;4BACxB,cAAc,OAAO,YAAY;wBACnC;kCAGA,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,OAAO,OAAO,KAAK;gCACnB,QAAQ,OAAO,MAAM;gCACrB,cAAc,CAAC,KAAK,EAAE,OAAO,YAAY,CAAC,OAAO,CAAC;4BACpD;;8CAGA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,QAAQ,OAAO,eAAe;oCAAC;8CAEvC;;;;;;8CAIH,6LAAC;oCAAI,WAAU;8CACZ;;;;;;gCAIF,aAAa,YACZ,sCAEA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,QAAQ,OAAO,mBAAmB;oCAAC;8CAE5C,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjC;GA5TgB;KAAA", "debugId": null}}, {"offset": {"line": 1657, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/proiecte/webappandroid/ai-mobile-platform/src/components/chat/ChatInterface.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useRef, useEffect } from \"react\";\nimport { ChatMessage } from \"@/types\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Send, Paperclip, Mic, Bo<PERSON>, User, Loader2 } from \"lucide-react\";\n\ninterface ChatInterfaceProps {\n  messages: ChatMessage[];\n  onSendMessage: (content: string, attachments?: File[]) => void;\n  isAiTyping?: boolean;\n  className?: string;\n}\n\nexport function ChatInterface({ \n  messages, \n  onSendMessage, \n  isAiTyping = false,\n  className = \"\"\n}: ChatInterfaceProps) {\n  const [inputMessage, setInputMessage] = useState(\"\");\n  const [attachments, setAttachments] = useState<File[]>([]);\n  const [isRecording, setIsRecording] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages, isAiTyping]);\n\n  const handleSendMessage = () => {\n    if (!inputMessage.trim() && attachments.length === 0) return;\n\n    onSendMessage(inputMessage, attachments);\n    setInputMessage(\"\");\n    setAttachments([]);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === \"Enter\" && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = Array.from(e.target.files || []);\n    setAttachments(prev => [...prev, ...files]);\n  };\n\n  const removeAttachment = (index: number) => {\n    setAttachments(prev => prev.filter((_, i) => i !== index));\n  };\n\n  const startVoiceRecording = () => {\n    setIsRecording(true);\n    // TODO: Implement voice recording\n    setTimeout(() => {\n      setIsRecording(false);\n      setInputMessage(\"Mesaj vocal transcris...\");\n    }, 2000);\n  };\n\n  return (\n    <div className={`flex flex-col h-full ${className}`}>\n      {/* Messages Area */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n        {messages.map((message) => (\n          <div\n            key={message.id}\n            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}\n          >\n            <div\n              className={`max-w-[80%] flex items-start space-x-3 ${\n                message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''\n              }`}\n            >\n              {/* Avatar */}\n              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${\n                message.type === 'user' \n                  ? 'bg-blue-600 text-white' \n                  : 'bg-purple-600 text-white'\n              }`}>\n                {message.type === 'user' ? (\n                  <User className=\"h-4 w-4\" />\n                ) : (\n                  <Bot className=\"h-4 w-4\" />\n                )}\n              </div>\n\n              {/* Message Content */}\n              <div\n                className={`p-4 rounded-lg ${\n                  message.type === 'user'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100'\n                }`}\n              >\n                <p className=\"whitespace-pre-wrap\">{message.content}</p>\n                \n                {/* Attachments */}\n                {message.attachments && message.attachments.length > 0 && (\n                  <div className=\"mt-2 space-y-1\">\n                    {message.attachments.map((file, index) => (\n                      <div key={index} className=\"text-xs opacity-80\">\n                        📎 {file.name}\n                      </div>\n                    ))}\n                  </div>\n                )}\n\n                {/* Metadata */}\n                <div className={`text-xs mt-2 ${\n                  message.type === 'user' ? 'text-blue-100' : 'text-gray-500'\n                }`}>\n                  {message.timestamp.toLocaleTimeString()}\n                  {message.metadata?.confidence && (\n                    <span className=\"ml-2\">\n                      Încredere: {Math.round(message.metadata.confidence * 100)}%\n                    </span>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n\n        {/* AI Typing Indicator */}\n        {isAiTyping && (\n          <div className=\"flex justify-start\">\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"flex-shrink-0 w-8 h-8 rounded-full bg-purple-600 text-white flex items-center justify-center\">\n                <Bot className=\"h-4 w-4\" />\n              </div>\n              <div className=\"bg-gray-100 dark:bg-gray-800 p-4 rounded-lg\">\n                <div className=\"flex items-center space-x-2\">\n                  <Loader2 className=\"h-4 w-4 animate-spin\" />\n                  <span className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    MobileGenius AI scrie...\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input Area */}\n      <div className=\"border-t p-4 space-y-3\">\n        {/* Attachments Preview */}\n        {attachments.length > 0 && (\n          <div className=\"flex flex-wrap gap-2\">\n            {attachments.map((file, index) => (\n              <div\n                key={index}\n                className=\"flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-full text-sm\"\n              >\n                <span>📎 {file.name}</span>\n                <button\n                  onClick={() => removeAttachment(index)}\n                  className=\"text-red-500 hover:text-red-700\"\n                >\n                  ×\n                </button>\n              </div>\n            ))}\n          </div>\n        )}\n\n        {/* Input Controls */}\n        <div className=\"flex space-x-2\">\n          {/* File Upload */}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => fileInputRef.current?.click()}\n            disabled={isAiTyping}\n          >\n            <Paperclip className=\"h-4 w-4\" />\n          </Button>\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            multiple\n            className=\"hidden\"\n            onChange={handleFileUpload}\n            accept=\"image/*,.pdf,.txt,.doc,.docx\"\n          />\n\n          {/* Voice Recording */}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={startVoiceRecording}\n            disabled={isAiTyping}\n            className={isRecording ? \"bg-red-100 text-red-600\" : \"\"}\n          >\n            <Mic className={`h-4 w-4 ${isRecording ? \"animate-pulse\" : \"\"}`} />\n          </Button>\n\n          {/* Text Input */}\n          <div className=\"flex-1 relative\">\n            <textarea\n              value={inputMessage}\n              onChange={(e) => setInputMessage(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Descrie-mi aplicația pe care vrei să o creezi...\"\n              className=\"w-full p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-600\"\n              rows={2}\n              disabled={isAiTyping}\n            />\n          </div>\n\n          {/* Send Button */}\n          <Button \n            onClick={handleSendMessage} \n            disabled={(!inputMessage.trim() && attachments.length === 0) || isAiTyping}\n            className=\"px-6\"\n          >\n            <Send className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;AAeO,SAAS,cAAc,EAC5B,QAAQ,EACR,aAAa,EACb,aAAa,KAAK,EAClB,YAAY,EAAE,EACK;;IACnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;QAAU;KAAW;IAEzB,MAAM,oBAAoB;QACxB,IAAI,CAAC,aAAa,IAAI,MAAM,YAAY,MAAM,KAAK,GAAG;QAEtD,cAAc,cAAc;QAC5B,gBAAgB;QAChB,eAAe,EAAE;IACnB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;QAC7C,eAAe,CAAA,OAAQ;mBAAI;mBAAS;aAAM;IAC5C;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACrD;IAEA,MAAM,sBAAsB;QAC1B,eAAe;QACf,kCAAkC;QAClC,WAAW;YACT,eAAe;YACf,gBAAgB;QAClB,GAAG;IACL;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,qBAAqB,EAAE,WAAW;;0BAEjD,6LAAC;gBAAI,WAAU;;oBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;4BAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;sCAE9E,cAAA,6LAAC;gCACC,WAAW,CAAC,uCAAuC,EACjD,QAAQ,IAAI,KAAK,SAAS,qCAAqC,IAC/D;;kDAGF,6LAAC;wCAAI,WAAW,CAAC,oEAAoE,EACnF,QAAQ,IAAI,KAAK,SACb,2BACA,4BACJ;kDACC,QAAQ,IAAI,KAAK,uBAChB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;iEAEhB,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAKnB,6LAAC;wCACC,WAAW,CAAC,eAAe,EACzB,QAAQ,IAAI,KAAK,SACb,2BACA,iEACJ;;0DAEF,6LAAC;gDAAE,WAAU;0DAAuB,QAAQ,OAAO;;;;;;4CAGlD,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,mBACnD,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9B,6LAAC;wDAAgB,WAAU;;4DAAqB;4DAC1C,KAAK,IAAI;;uDADL;;;;;;;;;;0DAQhB,6LAAC;gDAAI,WAAW,CAAC,aAAa,EAC5B,QAAQ,IAAI,KAAK,SAAS,kBAAkB,iBAC5C;;oDACC,QAAQ,SAAS,CAAC,kBAAkB;oDACpC,QAAQ,QAAQ,EAAE,4BACjB,6LAAC;wDAAK,WAAU;;4DAAO;4DACT,KAAK,KAAK,CAAC,QAAQ,QAAQ,CAAC,UAAU,GAAG;4DAAK;;;;;;;;;;;;;;;;;;;;;;;;;2BAjD/D,QAAQ,EAAE;;;;;oBA2DlB,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrE,6LAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,6LAAC;gBAAI,WAAU;;oBAEZ,YAAY,MAAM,GAAG,mBACpB,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;;4CAAK;4CAAI,KAAK,IAAI;;;;;;;kDACnB,6LAAC;wCACC,SAAS,IAAM,iBAAiB;wCAChC,WAAU;kDACX;;;;;;;+BAPI;;;;;;;;;;kCAgBb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,aAAa,OAAO,EAAE;gCACrC,UAAU;0CAEV,cAAA,6LAAC,+MAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;gCACC,KAAK;gCACL,MAAK;gCACL,QAAQ;gCACR,WAAU;gCACV,UAAU;gCACV,QAAO;;;;;;0CAIT,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAW,cAAc,4BAA4B;0CAErD,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,kBAAkB,IAAI;;;;;;;;;;;0CAIjE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,YAAY;oCACZ,aAAY;oCACZ,WAAU;oCACV,MAAM;oCACN,UAAU;;;;;;;;;;;0CAKd,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,AAAC,CAAC,aAAa,IAAI,MAAM,YAAY,MAAM,KAAK,KAAM;gCAChE,WAAU;0CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;GAzNgB;KAAA", "debugId": null}}, {"offset": {"line": 2054, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/proiecte/webappandroid/ai-mobile-platform/src/lib/ai/aiService.ts"], "sourcesContent": ["import { AIRequest, AIResponse, ChatMessage, CodeChange, Project } from \"@/types\";\n\nclass AIService {\n  private baseUrl = process.env.NEXT_PUBLIC_AI_API_URL || 'http://localhost:3004/api/ai';\n  private apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY;\n\n  async sendMessage(request: AIRequest): Promise<AIResponse> {\n    try {\n      const response = await fetch(`${this.baseUrl}/chat`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${this.apiKey}`\n        },\n        body: JSON.stringify(request)\n      });\n\n      if (!response.ok) {\n        throw new Error(`AI Service error: ${response.statusText}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error('AI Service error:', error);\n\n      // Fallback to mock response for development\n      return this.getMockResponse(request);\n    }\n  }\n\n  async generateCode(prompt: string, platform: string, context?: any): Promise<CodeChange[]> {\n    try {\n      const response = await fetch(`${this.baseUrl}/generate-code`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${this.apiKey}`\n        },\n        body: JSON.stringify({ prompt, platform, context })\n      });\n\n      if (!response.ok) {\n        throw new Error(`Code generation error: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      return data.codeChanges || [];\n    } catch (error) {\n      console.error('Code generation error:', error);\n\n      // Fallback to mock code generation\n      return this.getMockCodeGeneration(prompt, platform);\n    }\n  }\n\n  async analyzeProject(project: Project): Promise<{\n    suggestions: string[];\n    optimizations: string[];\n    issues: string[];\n  }> {\n    try {\n      const response = await fetch(`${this.baseUrl}/analyze-project`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${this.apiKey}`\n        },\n        body: JSON.stringify({ project })\n      });\n\n      if (!response.ok) {\n        throw new Error(`Project analysis error: ${response.statusText}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error('Project analysis error:', error);\n\n      return {\n        suggestions: [\n          \"Adaugă validare pentru input-urile utilizatorului\",\n          \"Implementează caching pentru performanță mai bună\",\n          \"Adaugă teste unitare pentru componentele principale\"\n        ],\n        optimizations: [\n          \"Optimizează imaginile pentru încărcare mai rapidă\",\n          \"Folosește lazy loading pentru componentele mari\",\n          \"Implementează code splitting\"\n        ],\n        issues: [\n          \"Lipsesc verificările de erori în unele funcții\",\n          \"Unele componente nu sunt responsive\"\n        ]\n      };\n    }\n  }\n\n  async improveCode(code: string, improvements: string[]): Promise<string> {\n    try {\n      const response = await fetch(`${this.baseUrl}/improve-code`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${this.apiKey}`\n        },\n        body: JSON.stringify({ code, improvements })\n      });\n\n      if (!response.ok) {\n        throw new Error(`Code improvement error: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      return data.improvedCode || code;\n    } catch (error) {\n      console.error('Code improvement error:', error);\n      return code; // Return original code if improvement fails\n    }\n  }\n\n  private getMockResponse(request: AIRequest): AIResponse {\n    const responses = [\n      {\n        message: `Excelentă idee! Pentru \"${request.message}\", îți sugerez să începem cu o arhitectură React Native pentru a acoperi atât Android cât și iOS. Vrei să discutăm despre funcționalitățile principale sau să trecem direct la dezvoltare?`,\n        suggestions: [\n          \"Să definim funcționalitățile principale\",\n          \"Să creez structura de bază a proiectului\",\n          \"Să discutăm despre design și UI\"\n        ],\n        confidence: 0.92\n      },\n      {\n        message: `Perfect! Voi crea o aplicație ${this.detectAppType(request.message)} cu toate funcționalitățile pe care le-ai menționat. Să încep cu structura de bază și apoi să adaug funcționalitățile una câte una.`,\n        suggestions: [\n          \"Începe cu structura de bază\",\n          \"Adaugă funcționalitățile principale\",\n          \"Configurează stilizarea\"\n        ],\n        confidence: 0.88\n      },\n      {\n        message: `Am înțeles cerințele tale. Voi implementa această funcționalitate pas cu pas. Poți urmări progresul în editorul de cod din dreapta.`,\n        suggestions: [\n          \"Continuă cu implementarea\",\n          \"Testează funcționalitatea\",\n          \"Adaugă validări\"\n        ],\n        confidence: 0.95\n      }\n    ];\n\n    return responses[Math.floor(Math.random() * responses.length)];\n  }\n\n  private getMockCodeGeneration(prompt: string, platform: string): CodeChange[] {\n    const appType = this.detectAppType(prompt);\n\n    if (appType === 'ecommerce') {\n      return [\n        {\n          file: 'App.tsx',\n          action: 'create',\n          content: this.getEcommerceAppCode(),\n          description: 'Aplicația principală de e-commerce cu navigare și componente de bază'\n        },\n        {\n          file: 'components/ProductCard.tsx',\n          action: 'create',\n          content: this.getProductCardCode(),\n          description: 'Componentă pentru afișarea produselor'\n        }\n      ];\n    } else if (appType === 'fitness') {\n      return [\n        {\n          file: 'App.tsx',\n          action: 'create',\n          content: this.getFitnessAppCode(),\n          description: 'Aplicația de fitness cu dashboard și tracking'\n        }\n      ];\n    } else if (appType === 'chat') {\n      return [\n        {\n          file: 'App.tsx',\n          action: 'create',\n          content: this.getChatAppCode(),\n          description: 'Aplicația de chat cu lista de conversații'\n        }\n      ];\n    }\n\n    return [\n      {\n        file: 'App.tsx',\n        action: 'create',\n        content: this.getDefaultAppCode(),\n        description: 'Aplicația de bază cu structura principală'\n      }\n    ];\n  }\n\n  private detectAppType(prompt: string): string {\n    const lowerPrompt = prompt.toLowerCase();\n\n    if (lowerPrompt.includes('magazin') || lowerPrompt.includes('ecommerce') || lowerPrompt.includes('shopping')) {\n      return 'ecommerce';\n    } else if (lowerPrompt.includes('fitness') || lowerPrompt.includes('antrenament') || lowerPrompt.includes('sport')) {\n      return 'fitness';\n    } else if (lowerPrompt.includes('chat') || lowerPrompt.includes('mesagerie') || lowerPrompt.includes('social')) {\n      return 'chat';\n    }\n\n    return 'default';\n  }\n\n  private getEcommerceAppCode(): string {\n    return `import React, { useState } from 'react';\nimport { View, Text, StyleSheet, TouchableOpacity, FlatList, Image } from 'react-native';\n\nconst ShoppingApp = () => {\n  const [products, setProducts] = useState([\n    { id: 1, name: 'Telefon Samsung Galaxy S24', price: 2500, image: 'phone.jpg', rating: 4.5 },\n    { id: 2, name: 'Laptop Dell XPS 13', price: 3500, image: 'laptop.jpg', rating: 4.8 },\n    { id: 3, name: 'Căști Sony WH-1000XM5', price: 450, image: 'headphones.jpg', rating: 4.7 },\n    { id: 4, name: 'Smartwatch Apple Watch', price: 1200, image: 'watch.jpg', rating: 4.6 }\n  ]);\n\n  const [cart, setCart] = useState([]);\n\n  const addToCart = (product) => {\n    setCart([...cart, product]);\n  };\n\n  const renderProduct = ({ item }) => (\n    <View style={styles.productCard}>\n      <View style={styles.productImageContainer}>\n        <Text style={styles.productImagePlaceholder}>📱</Text>\n      </View>\n      <Text style={styles.productName}>{item.name}</Text>\n      <Text style={styles.productPrice}>{item.price} RON</Text>\n      <View style={styles.ratingContainer}>\n        <Text style={styles.rating}>⭐ {item.rating}</Text>\n      </View>\n      <TouchableOpacity\n        style={styles.addButton}\n        onPress={() => addToCart(item)}\n      >\n        <Text style={styles.buttonText}>Adaugă în coș</Text>\n      </TouchableOpacity>\n    </View>\n  );\n\n  return (\n    <View style={styles.container}>\n      <Text style={styles.title}>Magazin Online</Text>\n      <FlatList\n        data={products}\n        renderItem={renderProduct}\n        keyExtractor={item => item.id.toString()}\n        style={styles.productList}\n        numColumns={2}\n      />\n      <View style={styles.cartInfo}>\n        <Text style={styles.cartText}>Produse în coș: {cart.length}</Text>\n        <TouchableOpacity style={styles.checkoutButton}>\n          <Text style={styles.checkoutText}>Vezi coșul</Text>\n        </TouchableOpacity>\n      </View>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    padding: 20,\n    backgroundColor: '#f5f5f5'\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    textAlign: 'center',\n    marginBottom: 20,\n    color: '#333'\n  },\n  productCard: {\n    backgroundColor: 'white',\n    padding: 15,\n    margin: 5,\n    borderRadius: 12,\n    shadowColor: '#000',\n    shadowOffset: { width: 0, height: 2 },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 3,\n    flex: 1\n  },\n  productImageContainer: {\n    alignItems: 'center',\n    marginBottom: 10\n  },\n  productImagePlaceholder: {\n    fontSize: 40\n  },\n  productName: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    marginBottom: 5\n  },\n  productPrice: {\n    fontSize: 18,\n    color: '#e74c3c',\n    fontWeight: 'bold',\n    marginBottom: 5\n  },\n  ratingContainer: {\n    marginBottom: 10\n  },\n  rating: {\n    fontSize: 14,\n    color: '#f39c12'\n  },\n  addButton: {\n    backgroundColor: '#3498db',\n    padding: 10,\n    borderRadius: 8,\n    alignItems: 'center'\n  },\n  buttonText: {\n    color: 'white',\n    fontWeight: 'bold',\n    fontSize: 14\n  },\n  productList: {\n    flex: 1\n  },\n  cartInfo: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    padding: 15,\n    backgroundColor: 'white',\n    borderRadius: 12,\n    marginTop: 10\n  },\n  cartText: {\n    fontSize: 16,\n    fontWeight: 'bold'\n  },\n  checkoutButton: {\n    backgroundColor: '#27ae60',\n    paddingHorizontal: 20,\n    paddingVertical: 10,\n    borderRadius: 8\n  },\n  checkoutText: {\n    color: 'white',\n    fontWeight: 'bold'\n  }\n});\n\nexport default ShoppingApp;`;\n  }\n\n  private getProductCardCode(): string {\n    return `import React from 'react';\nimport { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';\n\ninterface ProductCardProps {\n  product: {\n    id: number;\n    name: string;\n    price: number;\n    image: string;\n    rating: number;\n  };\n  onAddToCart: (product: any) => void;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({ product, onAddToCart }) => {\n  return (\n    <View style={styles.card}>\n      <View style={styles.imageContainer}>\n        <Text style={styles.imagePlaceholder}>📱</Text>\n      </View>\n      <Text style={styles.name}>{product.name}</Text>\n      <Text style={styles.price}>{product.price} RON</Text>\n      <Text style={styles.rating}>⭐ {product.rating}</Text>\n      <TouchableOpacity\n        style={styles.button}\n        onPress={() => onAddToCart(product)}\n      >\n        <Text style={styles.buttonText}>Adaugă în coș</Text>\n      </TouchableOpacity>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  card: {\n    backgroundColor: 'white',\n    padding: 15,\n    margin: 5,\n    borderRadius: 12,\n    shadowColor: '#000',\n    shadowOffset: { width: 0, height: 2 },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 3,\n    flex: 1\n  },\n  imageContainer: {\n    alignItems: 'center',\n    marginBottom: 10\n  },\n  imagePlaceholder: {\n    fontSize: 40\n  },\n  name: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    marginBottom: 5\n  },\n  price: {\n    fontSize: 18,\n    color: '#e74c3c',\n    fontWeight: 'bold',\n    marginBottom: 5\n  },\n  rating: {\n    fontSize: 14,\n    color: '#f39c12',\n    marginBottom: 10\n  },\n  button: {\n    backgroundColor: '#3498db',\n    padding: 10,\n    borderRadius: 8,\n    alignItems: 'center'\n  },\n  buttonText: {\n    color: 'white',\n    fontWeight: 'bold',\n    fontSize: 14\n  }\n});\n\nexport default ProductCard;`;\n  }\n\n  private getFitnessAppCode(): string {\n    return `import React, { useState, useEffect } from 'react';\nimport { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';\n\nconst FitnessApp = () => {\n  const [stats, setStats] = useState({\n    steps: 1247,\n    calories: 342,\n    activeMinutes: 45,\n    distance: 7.2\n  });\n\n  const [workouts, setWorkouts] = useState([\n    { id: 1, name: 'Cardio Morning', duration: 30, calories: 250, completed: true },\n    { id: 2, name: 'Strength Training', duration: 45, calories: 180, completed: false },\n    { id: 3, name: 'Yoga Session', duration: 60, calories: 120, completed: false }\n  ]);\n\n  return (\n    <ScrollView style={styles.container}>\n      <Text style={styles.title}>Fitness Tracker</Text>\n\n      <View style={styles.statsContainer}>\n        <View style={styles.statCard}>\n          <Text style={styles.statNumber}>{stats.steps}</Text>\n          <Text style={styles.statLabel}>Pași</Text>\n        </View>\n        <View style={styles.statCard}>\n          <Text style={styles.statNumber}>{stats.calories}</Text>\n          <Text style={styles.statLabel}>Calorii</Text>\n        </View>\n        <View style={styles.statCard}>\n          <Text style={styles.statNumber}>{stats.activeMinutes}</Text>\n          <Text style={styles.statLabel}>Min Active</Text>\n        </View>\n        <View style={styles.statCard}>\n          <Text style={styles.statNumber}>{stats.distance}</Text>\n          <Text style={styles.statLabel}>Km</Text>\n        </View>\n      </View>\n\n      <TouchableOpacity style={styles.startWorkoutButton}>\n        <Text style={styles.startWorkoutText}>Începe Antrenament</Text>\n      </TouchableOpacity>\n\n      <View style={styles.workoutsSection}>\n        <Text style={styles.sectionTitle}>Antrenamente Planificate</Text>\n        {workouts.map(workout => (\n          <View key={workout.id} style={styles.workoutCard}>\n            <View style={styles.workoutInfo}>\n              <Text style={styles.workoutName}>{workout.name}</Text>\n              <Text style={styles.workoutDetails}>\n                {workout.duration} min • {workout.calories} cal\n              </Text>\n            </View>\n            <TouchableOpacity\n              style={[\n                styles.workoutButton,\n                workout.completed && styles.completedButton\n              ]}\n            >\n              <Text style={styles.workoutButtonText}>\n                {workout.completed ? '✓' : 'Start'}\n              </Text>\n            </TouchableOpacity>\n          </View>\n        ))}\n      </View>\n    </ScrollView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f8f9fa'\n  },\n  title: {\n    fontSize: 28,\n    fontWeight: 'bold',\n    textAlign: 'center',\n    marginVertical: 20,\n    color: '#2c3e50'\n  },\n  statsContainer: {\n    flexDirection: 'row',\n    flexWrap: 'wrap',\n    justifyContent: 'space-between',\n    paddingHorizontal: 20,\n    marginBottom: 20\n  },\n  statCard: {\n    backgroundColor: 'white',\n    padding: 20,\n    borderRadius: 12,\n    width: '48%',\n    marginBottom: 10,\n    alignItems: 'center',\n    shadowColor: '#000',\n    shadowOffset: { width: 0, height: 2 },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 3\n  },\n  statNumber: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    color: '#3498db'\n  },\n  statLabel: {\n    fontSize: 14,\n    color: '#7f8c8d',\n    marginTop: 5\n  },\n  startWorkoutButton: {\n    backgroundColor: '#e74c3c',\n    marginHorizontal: 20,\n    padding: 15,\n    borderRadius: 12,\n    alignItems: 'center',\n    marginBottom: 30\n  },\n  startWorkoutText: {\n    color: 'white',\n    fontSize: 18,\n    fontWeight: 'bold'\n  },\n  workoutsSection: {\n    paddingHorizontal: 20\n  },\n  sectionTitle: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    marginBottom: 15,\n    color: '#2c3e50'\n  },\n  workoutCard: {\n    backgroundColor: 'white',\n    padding: 15,\n    borderRadius: 12,\n    marginBottom: 10,\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    shadowColor: '#000',\n    shadowOffset: { width: 0, height: 1 },\n    shadowOpacity: 0.1,\n    shadowRadius: 2,\n    elevation: 2\n  },\n  workoutInfo: {\n    flex: 1\n  },\n  workoutName: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: '#2c3e50'\n  },\n  workoutDetails: {\n    fontSize: 14,\n    color: '#7f8c8d',\n    marginTop: 2\n  },\n  workoutButton: {\n    backgroundColor: '#27ae60',\n    paddingHorizontal: 15,\n    paddingVertical: 8,\n    borderRadius: 8\n  },\n  completedButton: {\n    backgroundColor: '#95a5a6'\n  },\n  workoutButtonText: {\n    color: 'white',\n    fontWeight: 'bold'\n  }\n});\n\nexport default FitnessApp;`;\n  }\n\n  private getChatAppCode(): string {\n    return `import React, { useState } from 'react';\nimport { View, Text, StyleSheet, TouchableOpacity, FlatList, TextInput } from 'react-native';\n\nconst ChatApp = () => {\n  const [conversations, setConversations] = useState([\n    { id: 1, name: 'Ana Maria', lastMessage: 'Salut! Ce mai faci?', time: '14:30', unread: 2 },\n    { id: 2, name: 'Bogdan', lastMessage: 'Ne vedem mâine?', time: '13:15', unread: 0 },\n    { id: 3, name: 'Cristina', lastMessage: 'Mulțumesc pentru ajutor!', time: '12:45', unread: 1 },\n    { id: 4, name: 'David', lastMessage: 'Perfect, vorbim mai târziu', time: '11:20', unread: 0 }\n  ]);\n\n  const renderConversation = ({ item }) => (\n    <TouchableOpacity style={styles.conversationItem}>\n      <View style={styles.avatar}>\n        <Text style={styles.avatarText}>{item.name[0]}</Text>\n      </View>\n      <View style={styles.conversationContent}>\n        <View style={styles.conversationHeader}>\n          <Text style={styles.contactName}>{item.name}</Text>\n          <Text style={styles.messageTime}>{item.time}</Text>\n        </View>\n        <View style={styles.messagePreview}>\n          <Text style={styles.lastMessage} numberOfLines={1}>\n            {item.lastMessage}\n          </Text>\n          {item.unread > 0 && (\n            <View style={styles.unreadBadge}>\n              <Text style={styles.unreadText}>{item.unread}</Text>\n            </View>\n          )}\n        </View>\n      </View>\n    </TouchableOpacity>\n  );\n\n  return (\n    <View style={styles.container}>\n      <View style={styles.header}>\n        <Text style={styles.title}>Chat Social</Text>\n        <TouchableOpacity style={styles.newChatButton}>\n          <Text style={styles.newChatText}>+</Text>\n        </TouchableOpacity>\n      </View>\n\n      <View style={styles.searchContainer}>\n        <TextInput\n          style={styles.searchInput}\n          placeholder=\"Caută conversații...\"\n          placeholderTextColor=\"#999\"\n        />\n      </View>\n\n      <FlatList\n        data={conversations}\n        renderItem={renderConversation}\n        keyExtractor={item => item.id.toString()}\n        style={styles.conversationsList}\n      />\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f8f9fa'\n  },\n  header: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    padding: 20,\n    backgroundColor: 'white',\n    borderBottomWidth: 1,\n    borderBottomColor: '#e9ecef'\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    color: '#2c3e50'\n  },\n  newChatButton: {\n    backgroundColor: '#3498db',\n    width: 40,\n    height: 40,\n    borderRadius: 20,\n    justifyContent: 'center',\n    alignItems: 'center'\n  },\n  newChatText: {\n    color: 'white',\n    fontSize: 24,\n    fontWeight: 'bold'\n  },\n  searchContainer: {\n    padding: 15,\n    backgroundColor: 'white'\n  },\n  searchInput: {\n    backgroundColor: '#f1f3f4',\n    padding: 12,\n    borderRadius: 25,\n    fontSize: 16\n  },\n  conversationsList: {\n    flex: 1\n  },\n  conversationItem: {\n    flexDirection: 'row',\n    padding: 15,\n    backgroundColor: 'white',\n    borderBottomWidth: 1,\n    borderBottomColor: '#f1f3f4'\n  },\n  avatar: {\n    width: 50,\n    height: 50,\n    borderRadius: 25,\n    backgroundColor: '#3498db',\n    justifyContent: 'center',\n    alignItems: 'center',\n    marginRight: 15\n  },\n  avatarText: {\n    color: 'white',\n    fontSize: 18,\n    fontWeight: 'bold'\n  },\n  conversationContent: {\n    flex: 1\n  },\n  conversationHeader: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: 5\n  },\n  contactName: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: '#2c3e50'\n  },\n  messageTime: {\n    fontSize: 12,\n    color: '#7f8c8d'\n  },\n  messagePreview: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center'\n  },\n  lastMessage: {\n    fontSize: 14,\n    color: '#7f8c8d',\n    flex: 1\n  },\n  unreadBadge: {\n    backgroundColor: '#e74c3c',\n    borderRadius: 10,\n    minWidth: 20,\n    height: 20,\n    justifyContent: 'center',\n    alignItems: 'center',\n    marginLeft: 10\n  },\n  unreadText: {\n    color: 'white',\n    fontSize: 12,\n    fontWeight: 'bold'\n  }\n});\n\nexport default ChatApp;`;\n  }\n\n  private getDefaultAppCode(): string {\n    return `import React from 'react';\nimport { View, Text, StyleSheet, TouchableOpacity } from 'react-native';\n\nconst MyApp = () => {\n  return (\n    <View style={styles.container}>\n      <Text style={styles.title}>Aplicația Mea</Text>\n      <Text style={styles.subtitle}>Bine ai venit!</Text>\n\n      <View style={styles.buttonContainer}>\n        <TouchableOpacity style={styles.button}>\n          <Text style={styles.buttonText}>Începe</Text>\n        </TouchableOpacity>\n\n        <TouchableOpacity style={[styles.button, styles.secondaryButton]}>\n          <Text style={[styles.buttonText, styles.secondaryButtonText]}>Setări</Text>\n        </TouchableOpacity>\n      </View>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    backgroundColor: '#f8f9fa',\n    padding: 20\n  },\n  title: {\n    fontSize: 28,\n    fontWeight: 'bold',\n    color: '#2c3e50',\n    marginBottom: 10\n  },\n  subtitle: {\n    fontSize: 16,\n    color: '#7f8c8d',\n    marginBottom: 40\n  },\n  buttonContainer: {\n    width: '100%',\n    maxWidth: 300\n  },\n  button: {\n    backgroundColor: '#3498db',\n    padding: 15,\n    borderRadius: 8,\n    alignItems: 'center',\n    marginBottom: 15\n  },\n  buttonText: {\n    color: 'white',\n    fontSize: 16,\n    fontWeight: 'bold'\n  },\n  secondaryButton: {\n    backgroundColor: 'transparent',\n    borderWidth: 2,\n    borderColor: '#3498db'\n  },\n  secondaryButtonText: {\n    color: '#3498db'\n  }\n});\n\nexport default MyApp;`;\n  }\n}\n\nexport const aiService = new AIService();"], "names": [], "mappings": ";;;AAGoB;AADpB,MAAM;IACI,UAAU,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,+BAA+B;IAC/E,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;IAExD,MAAM,YAAY,OAAkB,EAAuB;QACzD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;gBAC1C;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,SAAS,UAAU,EAAE;YAC5D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YAEnC,4CAA4C;YAC5C,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B;IACF;IAEA,MAAM,aAAa,MAAc,EAAE,QAAgB,EAAE,OAAa,EAAyB;QACzF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;gBAC1C;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAQ;oBAAU;gBAAQ;YACnD;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;YACjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,WAAW,IAAI,EAAE;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YAExC,mCAAmC;YACnC,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ;QAC5C;IACF;IAEA,MAAM,eAAe,OAAgB,EAIlC;QACD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;gBAC1C;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAQ;YACjC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;YAClE;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,OAAO;gBACL,aAAa;oBACX;oBACA;oBACA;iBACD;gBACD,eAAe;oBACb;oBACA;oBACA;iBACD;gBACD,QAAQ;oBACN;oBACA;iBACD;YACH;QACF;IACF;IAEA,MAAM,YAAY,IAAY,EAAE,YAAsB,EAAmB;QACvE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;gBAC3D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;gBAC1C;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;gBAAa;YAC5C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;YAClE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,YAAY,IAAI;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,MAAM,4CAA4C;QAC3D;IACF;IAEQ,gBAAgB,OAAkB,EAAc;QACtD,MAAM,YAAY;YAChB;gBACE,SAAS,CAAC,wBAAwB,EAAE,QAAQ,OAAO,CAAC,0LAA0L,CAAC;gBAC/O,aAAa;oBACX;oBACA;oBACA;iBACD;gBACD,YAAY;YACd;YACA;gBACE,SAAS,CAAC,8BAA8B,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,OAAO,EAAE,mIAAmI,CAAC;gBAClN,aAAa;oBACX;oBACA;oBACA;iBACD;gBACD,YAAY;YACd;YACA;gBACE,SAAS,CAAC,mIAAmI,CAAC;gBAC9I,aAAa;oBACX;oBACA;oBACA;iBACD;gBACD,YAAY;YACd;SACD;QAED,OAAO,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;IAChE;IAEQ,sBAAsB,MAAc,EAAE,QAAgB,EAAgB;QAC5E,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC;QAEnC,IAAI,YAAY,aAAa;YAC3B,OAAO;gBACL;oBACE,MAAM;oBACN,QAAQ;oBACR,SAAS,IAAI,CAAC,mBAAmB;oBACjC,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,QAAQ;oBACR,SAAS,IAAI,CAAC,kBAAkB;oBAChC,aAAa;gBACf;aACD;QACH,OAAO,IAAI,YAAY,WAAW;YAChC,OAAO;gBACL;oBACE,MAAM;oBACN,QAAQ;oBACR,SAAS,IAAI,CAAC,iBAAiB;oBAC/B,aAAa;gBACf;aACD;QACH,OAAO,IAAI,YAAY,QAAQ;YAC7B,OAAO;gBACL;oBACE,MAAM;oBACN,QAAQ;oBACR,SAAS,IAAI,CAAC,cAAc;oBAC5B,aAAa;gBACf;aACD;QACH;QAEA,OAAO;YACL;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS,IAAI,CAAC,iBAAiB;gBAC/B,aAAa;YACf;SACD;IACH;IAEQ,cAAc,MAAc,EAAU;QAC5C,MAAM,cAAc,OAAO,WAAW;QAEtC,IAAI,YAAY,QAAQ,CAAC,cAAc,YAAY,QAAQ,CAAC,gBAAgB,YAAY,QAAQ,CAAC,aAAa;YAC5G,OAAO;QACT,OAAO,IAAI,YAAY,QAAQ,CAAC,cAAc,YAAY,QAAQ,CAAC,kBAAkB,YAAY,QAAQ,CAAC,UAAU;YAClH,OAAO;QACT,OAAO,IAAI,YAAY,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC,gBAAgB,YAAY,QAAQ,CAAC,WAAW;YAC9G,OAAO;QACT;QAEA,OAAO;IACT;IAEQ,sBAA8B;QACpC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAiJe,CAAC;IAC1B;IAEQ,qBAA6B;QACnC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAkFe,CAAC;IAC1B;IAEQ,oBAA4B;QAClC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiLc,CAAC;IACzB;IAEQ,iBAAyB;QAC/B,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA4KW,CAAC;IACtB;IAEQ,oBAA4B;QAClC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qBAmES,CAAC;IACpB;AACF;AAEO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 2922, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/proiecte/webappandroid/ai-mobile-platform/src/lib/websocket/websocketService.ts"], "sourcesContent": ["import { io, Socket } from 'socket.io-client';\nimport { WebSocketMessage, ChatMessage, CodeChange, BuildLog } from '@/types';\n\nclass WebSocketService {\n  private socket: Socket | null = null;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n  private reconnectDelay = 1000;\n\n  connect(url: string = process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'http://localhost:3001') {\n    if (this.socket?.connected) {\n      return;\n    }\n\n    this.socket = io(url, {\n      transports: ['websocket'],\n      autoConnect: true,\n      reconnection: true,\n      reconnectionAttempts: this.maxReconnectAttempts,\n      reconnectionDelay: this.reconnectDelay,\n    });\n\n    this.setupEventListeners();\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n    }\n  }\n\n  private setupEventListeners() {\n    if (!this.socket) return;\n\n    this.socket.on('connect', () => {\n      console.log('WebSocket connected');\n      this.reconnectAttempts = 0;\n    });\n\n    this.socket.on('disconnect', (reason) => {\n      console.log('WebSocket disconnected:', reason);\n    });\n\n    this.socket.on('connect_error', (error) => {\n      console.error('WebSocket connection error:', error);\n      this.handleReconnect();\n    });\n\n    this.socket.on('reconnect', (attemptNumber) => {\n      console.log('WebSocket reconnected after', attemptNumber, 'attempts');\n    });\n\n    this.socket.on('reconnect_error', (error) => {\n      console.error('WebSocket reconnection error:', error);\n    });\n  }\n\n  private handleReconnect() {\n    if (this.reconnectAttempts < this.maxReconnectAttempts) {\n      this.reconnectAttempts++;\n      setTimeout(() => {\n        if (this.socket && !this.socket.connected) {\n          this.socket.connect();\n        }\n      }, this.reconnectDelay * this.reconnectAttempts);\n    }\n  }\n\n  // Chat Events\n  sendChatMessage(message: ChatMessage, projectId?: string) {\n    this.emit('chat:message', { message, projectId });\n  }\n\n  onChatMessage(callback: (message: ChatMessage) => void) {\n    this.on('chat:message', callback);\n  }\n\n  onAITyping(callback: (isTyping: boolean) => void) {\n    this.on('ai:typing', callback);\n  }\n\n  // Code Events\n  sendCodeUpdate(codeChanges: CodeChange[], projectId: string) {\n    this.emit('code:update', { codeChanges, projectId });\n  }\n\n  onCodeUpdate(callback: (codeChanges: CodeChange[]) => void) {\n    this.on('code:update', callback);\n  }\n\n  onCodeGenerated(callback: (code: string, fileId: string) => void) {\n    this.on('code:generated', callback);\n  }\n\n  // Build Events\n  startBuild(projectId: string, platform: string) {\n    this.emit('build:start', { projectId, platform });\n  }\n\n  onBuildStatus(callback: (status: 'started' | 'progress' | 'completed' | 'failed', data?: any) => void) {\n    this.on('build:status', callback);\n  }\n\n  onBuildLog(callback: (log: BuildLog) => void) {\n    this.on('build:log', callback);\n  }\n\n  // Project Events\n  joinProject(projectId: string) {\n    this.emit('project:join', { projectId });\n  }\n\n  leaveProject(projectId: string) {\n    this.emit('project:leave', { projectId });\n  }\n\n  onProjectUpdate(callback: (projectData: any) => void) {\n    this.on('project:update', callback);\n  }\n\n  // AI Learning Events\n  sendFeedback(feedback: {\n    messageId: string;\n    rating: number;\n    comment?: string;\n    projectId?: string;\n  }) {\n    this.emit('ai:feedback', feedback);\n  }\n\n  onAILearningUpdate(callback: (learningData: any) => void) {\n    this.on('ai:learning:update', callback);\n  }\n\n  // Generic event handlers\n  private emit(event: string, data: any) {\n    if (this.socket?.connected) {\n      this.socket.emit(event, data);\n    } else {\n      console.warn('WebSocket not connected. Cannot emit event:', event);\n    }\n  }\n\n  private on(event: string, callback: (data: any) => void) {\n    if (this.socket) {\n      this.socket.on(event, callback);\n    }\n  }\n\n  private off(event: string, callback?: (data: any) => void) {\n    if (this.socket) {\n      this.socket.off(event, callback);\n    }\n  }\n\n  // Utility methods\n  isConnected(): boolean {\n    return this.socket?.connected || false;\n  }\n\n  getConnectionState(): 'connected' | 'disconnected' | 'connecting' | 'reconnecting' {\n    if (!this.socket) return 'disconnected';\n    \n    if (this.socket.connected) return 'connected';\n    if (this.socket.connecting) return 'connecting';\n    if (this.reconnectAttempts > 0) return 'reconnecting';\n    \n    return 'disconnected';\n  }\n\n  // Room management for collaborative features\n  joinRoom(roomId: string) {\n    this.emit('room:join', { roomId });\n  }\n\n  leaveRoom(roomId: string) {\n    this.emit('room:leave', { roomId });\n  }\n\n  onUserJoined(callback: (user: any) => void) {\n    this.on('room:user:joined', callback);\n  }\n\n  onUserLeft(callback: (user: any) => void) {\n    this.on('room:user:left', callback);\n  }\n\n  // Cleanup\n  removeAllListeners() {\n    if (this.socket) {\n      this.socket.removeAllListeners();\n    }\n  }\n\n  removeListener(event: string, callback?: (data: any) => void) {\n    this.off(event, callback);\n  }\n}\n\n// Singleton instance\nexport const websocketService = new WebSocketService();\n\n// React hook for WebSocket\nexport function useWebSocket() {\n  const connect = (url?: string) => websocketService.connect(url);\n  const disconnect = () => websocketService.disconnect();\n  const isConnected = () => websocketService.isConnected();\n  const getConnectionState = () => websocketService.getConnectionState();\n\n  return {\n    connect,\n    disconnect,\n    isConnected,\n    getConnectionState,\n    \n    // Chat\n    sendChatMessage: websocketService.sendChatMessage.bind(websocketService),\n    onChatMessage: websocketService.onChatMessage.bind(websocketService),\n    onAITyping: websocketService.onAITyping.bind(websocketService),\n    \n    // Code\n    sendCodeUpdate: websocketService.sendCodeUpdate.bind(websocketService),\n    onCodeUpdate: websocketService.onCodeUpdate.bind(websocketService),\n    onCodeGenerated: websocketService.onCodeGenerated.bind(websocketService),\n    \n    // Build\n    startBuild: websocketService.startBuild.bind(websocketService),\n    onBuildStatus: websocketService.onBuildStatus.bind(websocketService),\n    onBuildLog: websocketService.onBuildLog.bind(websocketService),\n    \n    // Project\n    joinProject: websocketService.joinProject.bind(websocketService),\n    leaveProject: websocketService.leaveProject.bind(websocketService),\n    onProjectUpdate: websocketService.onProjectUpdate.bind(websocketService),\n    \n    // AI Learning\n    sendFeedback: websocketService.sendFeedback.bind(websocketService),\n    onAILearningUpdate: websocketService.onAILearningUpdate.bind(websocketService),\n    \n    // Rooms\n    joinRoom: websocketService.joinRoom.bind(websocketService),\n    leaveRoom: websocketService.leaveRoom.bind(websocketService),\n    onUserJoined: websocketService.onUserJoined.bind(websocketService),\n    onUserLeft: websocketService.onUserLeft.bind(websocketService),\n    \n    // Cleanup\n    removeAllListeners: websocketService.removeAllListeners.bind(websocketService),\n    removeListener: websocketService.removeListener.bind(websocketService),\n  };\n}\n"], "names": [], "mappings": ";;;;AASwB;AATxB;AAAA;;AAGA,MAAM;IACI,SAAwB,KAAK;IAC7B,oBAAoB,EAAE;IACtB,uBAAuB,EAAE;IACzB,iBAAiB,KAAK;IAE9B,QAAQ,MAAc,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,uBAAuB,EAAE;QACtF,IAAI,IAAI,CAAC,MAAM,EAAE,WAAW;YAC1B;QACF;QAEA,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,kLAAA,CAAA,KAAE,AAAD,EAAE,KAAK;YACpB,YAAY;gBAAC;aAAY;YACzB,aAAa;YACb,cAAc;YACd,sBAAsB,IAAI,CAAC,oBAAoB;YAC/C,mBAAmB,IAAI,CAAC,cAAc;QACxC;QAEA,IAAI,CAAC,mBAAmB;IAC1B;IAEA,aAAa;QACX,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;QAChB;IACF;IAEQ,sBAAsB;QAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAElB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;YACxB,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,iBAAiB,GAAG;QAC3B;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC;YAC5B,QAAQ,GAAG,CAAC,2BAA2B;QACzC;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC;YAC/B,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,CAAC,eAAe;QACtB;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC;YAC3B,QAAQ,GAAG,CAAC,+BAA+B,eAAe;QAC5D;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,mBAAmB,CAAC;YACjC,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEQ,kBAAkB;QACxB,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE;YACtD,IAAI,CAAC,iBAAiB;YACtB,WAAW;gBACT,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;oBACzC,IAAI,CAAC,MAAM,CAAC,OAAO;gBACrB;YACF,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB;QACjD;IACF;IAEA,cAAc;IACd,gBAAgB,OAAoB,EAAE,SAAkB,EAAE;QACxD,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE;YAAS;QAAU;IACjD;IAEA,cAAc,QAAwC,EAAE;QACtD,IAAI,CAAC,EAAE,CAAC,gBAAgB;IAC1B;IAEA,WAAW,QAAqC,EAAE;QAChD,IAAI,CAAC,EAAE,CAAC,aAAa;IACvB;IAEA,cAAc;IACd,eAAe,WAAyB,EAAE,SAAiB,EAAE;QAC3D,IAAI,CAAC,IAAI,CAAC,eAAe;YAAE;YAAa;QAAU;IACpD;IAEA,aAAa,QAA6C,EAAE;QAC1D,IAAI,CAAC,EAAE,CAAC,eAAe;IACzB;IAEA,gBAAgB,QAAgD,EAAE;QAChE,IAAI,CAAC,EAAE,CAAC,kBAAkB;IAC5B;IAEA,eAAe;IACf,WAAW,SAAiB,EAAE,QAAgB,EAAE;QAC9C,IAAI,CAAC,IAAI,CAAC,eAAe;YAAE;YAAW;QAAS;IACjD;IAEA,cAAc,QAAuF,EAAE;QACrG,IAAI,CAAC,EAAE,CAAC,gBAAgB;IAC1B;IAEA,WAAW,QAAiC,EAAE;QAC5C,IAAI,CAAC,EAAE,CAAC,aAAa;IACvB;IAEA,iBAAiB;IACjB,YAAY,SAAiB,EAAE;QAC7B,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE;QAAU;IACxC;IAEA,aAAa,SAAiB,EAAE;QAC9B,IAAI,CAAC,IAAI,CAAC,iBAAiB;YAAE;QAAU;IACzC;IAEA,gBAAgB,QAAoC,EAAE;QACpD,IAAI,CAAC,EAAE,CAAC,kBAAkB;IAC5B;IAEA,qBAAqB;IACrB,aAAa,QAKZ,EAAE;QACD,IAAI,CAAC,IAAI,CAAC,eAAe;IAC3B;IAEA,mBAAmB,QAAqC,EAAE;QACxD,IAAI,CAAC,EAAE,CAAC,sBAAsB;IAChC;IAEA,yBAAyB;IACjB,KAAK,KAAa,EAAE,IAAS,EAAE;QACrC,IAAI,IAAI,CAAC,MAAM,EAAE,WAAW;YAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;QAC1B,OAAO;YACL,QAAQ,IAAI,CAAC,+CAA+C;QAC9D;IACF;IAEQ,GAAG,KAAa,EAAE,QAA6B,EAAE;QACvD,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO;QACxB;IACF;IAEQ,IAAI,KAAa,EAAE,QAA8B,EAAE;QACzD,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO;QACzB;IACF;IAEA,kBAAkB;IAClB,cAAuB;QACrB,OAAO,IAAI,CAAC,MAAM,EAAE,aAAa;IACnC;IAEA,qBAAmF;QACjF,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO;QAEzB,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO;QAClC,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO;QACnC,IAAI,IAAI,CAAC,iBAAiB,GAAG,GAAG,OAAO;QAEvC,OAAO;IACT;IAEA,6CAA6C;IAC7C,SAAS,MAAc,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,aAAa;YAAE;QAAO;IAClC;IAEA,UAAU,MAAc,EAAE;QACxB,IAAI,CAAC,IAAI,CAAC,cAAc;YAAE;QAAO;IACnC;IAEA,aAAa,QAA6B,EAAE;QAC1C,IAAI,CAAC,EAAE,CAAC,oBAAoB;IAC9B;IAEA,WAAW,QAA6B,EAAE;QACxC,IAAI,CAAC,EAAE,CAAC,kBAAkB;IAC5B;IAEA,UAAU;IACV,qBAAqB;QACnB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAChC;IACF;IAEA,eAAe,KAAa,EAAE,QAA8B,EAAE;QAC5D,IAAI,CAAC,GAAG,CAAC,OAAO;IAClB;AACF;AAGO,MAAM,mBAAmB,IAAI;AAG7B,SAAS;IACd,MAAM,UAAU,CAAC,MAAiB,iBAAiB,OAAO,CAAC;IAC3D,MAAM,aAAa,IAAM,iBAAiB,UAAU;IACpD,MAAM,cAAc,IAAM,iBAAiB,WAAW;IACtD,MAAM,qBAAqB,IAAM,iBAAiB,kBAAkB;IAEpE,OAAO;QACL;QACA;QACA;QACA;QAEA,OAAO;QACP,iBAAiB,iBAAiB,eAAe,CAAC,IAAI,CAAC;QACvD,eAAe,iBAAiB,aAAa,CAAC,IAAI,CAAC;QACnD,YAAY,iBAAiB,UAAU,CAAC,IAAI,CAAC;QAE7C,OAAO;QACP,gBAAgB,iBAAiB,cAAc,CAAC,IAAI,CAAC;QACrD,cAAc,iBAAiB,YAAY,CAAC,IAAI,CAAC;QACjD,iBAAiB,iBAAiB,eAAe,CAAC,IAAI,CAAC;QAEvD,QAAQ;QACR,YAAY,iBAAiB,UAAU,CAAC,IAAI,CAAC;QAC7C,eAAe,iBAAiB,aAAa,CAAC,IAAI,CAAC;QACnD,YAAY,iBAAiB,UAAU,CAAC,IAAI,CAAC;QAE7C,UAAU;QACV,aAAa,iBAAiB,WAAW,CAAC,IAAI,CAAC;QAC/C,cAAc,iBAAiB,YAAY,CAAC,IAAI,CAAC;QACjD,iBAAiB,iBAAiB,eAAe,CAAC,IAAI,CAAC;QAEvD,cAAc;QACd,cAAc,iBAAiB,YAAY,CAAC,IAAI,CAAC;QACjD,oBAAoB,iBAAiB,kBAAkB,CAAC,IAAI,CAAC;QAE7D,QAAQ;QACR,UAAU,iBAAiB,QAAQ,CAAC,IAAI,CAAC;QACzC,WAAW,iBAAiB,SAAS,CAAC,IAAI,CAAC;QAC3C,cAAc,iBAAiB,YAAY,CAAC,IAAI,CAAC;QACjD,YAAY,iBAAiB,UAAU,CAAC,IAAI,CAAC;QAE7C,UAAU;QACV,oBAAoB,iBAAiB,kBAAkB,CAAC,IAAI,CAAC;QAC7D,gBAAgB,iBAAiB,cAAc,CAAC,IAAI,CAAC;IACvD;AACF", "debugId": null}}, {"offset": {"line": 3151, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/proiecte/webappandroid/ai-mobile-platform/src/app/workspace/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { ArrowLeft, Play, Pause, Download, Settings, Smartphone, Code, MessageSquare } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { CodeEditor } from \"@/components/code-editor/CodeEditor\";\nimport { MobileSimulator } from \"@/components/mobile-preview/MobileSimulator\";\nimport { ChatInterface } from \"@/components/chat/ChatInterface\";\nimport { CodeFile, ChatMessage, Project, WorkspaceState } from \"@/types\";\nimport { aiService } from \"@/lib/ai/aiService\";\nimport { useWebSocket } from \"@/lib/websocket/websocketService\";\n\nexport default function WorkspacePage() {\n  const [workspaceState, setWorkspaceState] = useState<WorkspaceState>({\n    previewPlatform: 'android',\n    isBuilding: false,\n    buildLogs: [],\n    chatMessages: []\n  });\n\n  const [files, setFiles] = useState<CodeFile[]>([\n    {\n      id: '1',\n      name: 'App.tsx',\n      path: '/src/App.tsx',\n      content: `import React, { useState } from 'react';\nimport { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';\n\nconst ShoppingApp = () => {\n  const [products, setProducts] = useState([\n    { id: 1, name: 'Telefon Samsung', price: 2500, image: 'phone.jpg' },\n    { id: 2, name: 'Laptop Dell', price: 3500, image: 'laptop.jpg' },\n    { id: 3, name: 'Căști Sony', price: 450, image: 'headphones.jpg' }\n  ]);\n\n  const [cart, setCart] = useState([]);\n\n  const addToCart = (product) => {\n    setCart([...cart, product]);\n  };\n\n  const renderProduct = ({ item }) => (\n    <View style={styles.productCard}>\n      <Text style={styles.productName}>{item.name}</Text>\n      <Text style={styles.productPrice}>{item.price} RON</Text>\n      <TouchableOpacity\n        style={styles.addButton}\n        onPress={() => addToCart(item)}\n      >\n        <Text style={styles.buttonText}>Adaugă în coș</Text>\n      </TouchableOpacity>\n    </View>\n  );\n\n  return (\n    <View style={styles.container}>\n      <Text style={styles.title}>Magazin Online</Text>\n      <FlatList\n        data={products}\n        renderItem={renderProduct}\n        keyExtractor={item => item.id.toString()}\n        style={styles.productList}\n      />\n      <View style={styles.cartInfo}>\n        <Text>Produse în coș: {cart.length}</Text>\n      </View>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    padding: 20,\n    backgroundColor: '#f5f5f5'\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    textAlign: 'center',\n    marginBottom: 20\n  },\n  productCard: {\n    backgroundColor: 'white',\n    padding: 15,\n    marginBottom: 10,\n    borderRadius: 8,\n    shadowColor: '#000',\n    shadowOffset: { width: 0, height: 2 },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 3\n  },\n  productName: {\n    fontSize: 18,\n    fontWeight: 'bold'\n  },\n  productPrice: {\n    fontSize: 16,\n    color: '#666',\n    marginVertical: 5\n  },\n  addButton: {\n    backgroundColor: '#007bff',\n    padding: 10,\n    borderRadius: 5,\n    alignItems: 'center'\n  },\n  buttonText: {\n    color: 'white',\n    fontWeight: 'bold'\n  },\n  productList: {\n    flex: 1\n  },\n  cartInfo: {\n    padding: 15,\n    backgroundColor: 'white',\n    borderRadius: 8,\n    marginTop: 10\n  }\n});\n\nexport default ShoppingApp;`,\n      language: 'typescript',\n      lastModified: new Date()\n    }\n  ]);\n\n  const [activeFileId, setActiveFileId] = useState<string>('1');\n  const { startBuild, onBuildStatus, onBuildLog } = useWebSocket();\n\n  useEffect(() => {\n    // Setup WebSocket listeners for build events\n    onBuildStatus((status, data) => {\n      setWorkspaceState(prev => ({\n        ...prev,\n        isBuilding: status === 'started' || status === 'progress'\n      }));\n    });\n\n    onBuildLog((log) => {\n      setWorkspaceState(prev => ({\n        ...prev,\n        buildLogs: [...prev.buildLogs, log]\n      }));\n    });\n  }, []);\n\n  const buildApp = () => {\n    setWorkspaceState(prev => ({ ...prev, isBuilding: true }));\n    startBuild('current-project', workspaceState.previewPlatform);\n\n    // Fallback timeout in case WebSocket doesn't respond\n    setTimeout(() => {\n      setWorkspaceState(prev => ({ ...prev, isBuilding: false }));\n    }, 5000);\n  };\n\n  const handleFileChange = (fileId: string, content: string) => {\n    setFiles(prev => prev.map(file =>\n      file.id === fileId\n        ? { ...file, content, lastModified: new Date() }\n        : file\n    ));\n  };\n\n  const handleFileCreate = (name: string, language: CodeFile['language']) => {\n    const newFile: CodeFile = {\n      id: Date.now().toString(),\n      name,\n      path: `/src/${name}`,\n      content: getTemplateForLanguage(language),\n      language,\n      lastModified: new Date()\n    };\n    setFiles(prev => [...prev, newFile]);\n    setActiveFileId(newFile.id);\n  };\n\n  const handleFileDelete = (fileId: string) => {\n    if (files.length <= 1) return; // Don't delete the last file\n\n    setFiles(prev => prev.filter(file => file.id !== fileId));\n    if (activeFileId === fileId) {\n      setActiveFileId(files.find(f => f.id !== fileId)?.id || '');\n    }\n  };\n\n  const handleChatMessage = async (content: string, attachments?: File[]) => {\n    const userMessage: ChatMessage = {\n      id: Date.now().toString(),\n      type: 'user',\n      content,\n      timestamp: new Date(),\n      attachments\n    };\n\n    setWorkspaceState(prev => ({\n      ...prev,\n      chatMessages: [...prev.chatMessages, userMessage]\n    }));\n\n    // Process AI request with current code context\n    try {\n      const activeFile = files.find(f => f.id === activeFileId);\n      const response = await aiService.sendMessage({\n        message: content,\n        context: {\n          currentCode: activeFile?.content,\n          platform: workspaceState.previewPlatform\n        }\n      });\n\n      const aiMessage: ChatMessage = {\n        id: (Date.now() + 1).toString(),\n        type: 'ai',\n        content: response.message,\n        timestamp: new Date(),\n        metadata: {\n          confidence: response.confidence\n        }\n      };\n\n      setWorkspaceState(prev => ({\n        ...prev,\n        chatMessages: [...prev.chatMessages, aiMessage]\n      }));\n\n      // If AI suggests code changes, apply them\n      if (response.codeChanges && response.codeChanges.length > 0) {\n        response.codeChanges.forEach(change => {\n          if (change.action === 'create') {\n            handleFileCreate(change.file, 'typescript');\n          } else if (change.action === 'update' && change.content) {\n            const targetFile = files.find(f => f.name === change.file);\n            if (targetFile) {\n              handleFileChange(targetFile.id, change.content);\n            }\n          }\n        });\n      }\n    } catch (error) {\n      console.error('Error processing chat message:', error);\n    }\n  };\n\n  const getTemplateForLanguage = (language: CodeFile['language']): string => {\n    switch (language) {\n      case 'typescript':\n        return `import React from 'react';\n\nconst NewComponent = () => {\n  return (\n    <div>\n      <h1>New Component</h1>\n    </div>\n  );\n};\n\nexport default NewComponent;`;\n      case 'javascript':\n        return `import React from 'react';\n\nconst NewComponent = () => {\n  return (\n    <div>\n      <h1>New Component</h1>\n    </div>\n  );\n};\n\nexport default NewComponent;`;\n      case 'kotlin':\n        return `package com.example.myapp\n\nimport android.os.Bundle\nimport androidx.activity.ComponentActivity\n\nclass MainActivity : ComponentActivity() {\n    override fun onCreate(savedInstanceState: Bundle?) {\n        super.onCreate(savedInstanceState)\n        // Your code here\n    }\n}`;\n      case 'swift':\n        return `import UIKit\n\nclass ViewController: UIViewController {\n    override func viewDidLoad() {\n        super.viewDidLoad()\n        // Your code here\n    }\n}`;\n      case 'dart':\n        return `import 'package:flutter/material.dart';\n\nclass MyWidget extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('My App')),\n      body: Center(child: Text('Hello World')),\n    );\n  }\n}`;\n      default:\n        return '';\n    }\n  };\n\n  const activeFile = files.find(f => f.id === activeFileId);\n  const appCode = activeFile?.content || '';\n\n  return (\n    <div className=\"min-h-screen bg-gray-100 dark:bg-gray-900\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-8\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\">\n              <Button variant=\"ghost\" size=\"sm\" className=\"mr-4\">\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                Înapoi\n              </Button>\n            </Link>\n            <div>\n              <h1 className=\"text-2xl font-bold\">Workspace Dezvoltare</h1>\n              <p className=\"text-gray-600 dark:text-gray-300\">\n                {activeFile?.name || 'Aplicație'} - React Native\n              </p>\n            </div>\n          </div>\n          <div className=\"flex space-x-2\">\n            <Button variant=\"outline\" onClick={buildApp} disabled={workspaceState.isBuilding}>\n              {workspaceState.isBuilding ? <Pause className=\"h-4 w-4 mr-2\" /> : <Play className=\"h-4 w-4 mr-2\" />}\n              {workspaceState.isBuilding ? 'Building...' : 'Build App'}\n            </Button>\n            <Button variant=\"outline\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Export\n            </Button>\n            <Button variant=\"outline\">\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Workspace Grid */}\n        <div className=\"grid grid-cols-12 gap-6 h-[calc(100vh-200px)]\">\n          {/* Chat Panel */}\n          <div className=\"col-span-3\">\n            <Card className=\"h-full\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <MessageSquare className=\"h-5 w-5 mr-2\" />\n                  Chat Rapid\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"h-full p-0\">\n                <ChatInterface\n                  messages={workspaceState.chatMessages}\n                  onSendMessage={handleChatMessage}\n                  isAiTyping={false}\n                  className=\"h-full\"\n                />\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Code Editor */}\n          <div className=\"col-span-5\">\n            <Card className=\"h-full\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Code className=\"h-5 w-5 mr-2\" />\n                  Editor Cod\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"h-full p-0\">\n                <CodeEditor\n                  files={files}\n                  activeFileId={activeFileId}\n                  onFileChange={handleFileChange}\n                  onFileSelect={setActiveFileId}\n                  onFileCreate={handleFileCreate}\n                  onFileDelete={handleFileDelete}\n                  className=\"h-full\"\n                />\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Mobile Preview */}\n          <div className=\"col-span-4\">\n            <Card className=\"h-full\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <Smartphone className=\"h-5 w-5 mr-2\" />\n                    Preview Aplicație\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <Button\n                      variant={workspaceState.previewPlatform === 'android' ? 'default' : 'outline'}\n                      size=\"sm\"\n                      onClick={() => setWorkspaceState(prev => ({ ...prev, previewPlatform: 'android' }))}\n                    >\n                      Android\n                    </Button>\n                    <Button\n                      variant={workspaceState.previewPlatform === 'ios' ? 'default' : 'outline'}\n                      size=\"sm\"\n                      onClick={() => setWorkspaceState(prev => ({ ...prev, previewPlatform: 'ios' }))}\n                    >\n                      iOS\n                    </Button>\n                  </div>\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"flex justify-center items-start pt-4\">\n                <MobileSimulator\n                  platform={workspaceState.previewPlatform}\n                  appCode={appCode}\n                  onInteraction={(event) => {\n                    console.log('Simulator interaction:', event);\n                  }}\n                />\n\n                {/* Build Status Overlay */}\n                {workspaceState.isBuilding && (\n                  <div className=\"absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center\">\n                    <div className=\"bg-white p-4 rounded-lg text-center\">\n                      <div className=\"animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-2\"></div>\n                      <p className=\"text-sm\">Building aplicația...</p>\n                    </div>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n\n        {/* Bottom Panel */}\n        <div className=\"mt-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Progres Dezvoltare</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-600\">✓</div>\n                  <p className=\"text-sm\">Structura de bază</p>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-600\">✓</div>\n                  <p className=\"text-sm\">Componente UI</p>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-600\">⚡</div>\n                  <p className=\"text-sm\">Funcționalități</p>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-gray-400\">○</div>\n                  <p className=\"text-sm\">Testing</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;;;AAZA;;;;;;;;;;;AAce,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACnE,iBAAiB;QACjB,YAAY;QACZ,WAAW,EAAE;QACb,cAAc,EAAE;IAClB;IAEA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QAC7C;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAkGW,CAAC;YACtB,UAAU;YACV,cAAc,IAAI;QACpB;KACD;IAED,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,6CAA6C;YAC7C;2CAAc,CAAC,QAAQ;oBACrB;mDAAkB,CAAA,OAAQ,CAAC;gCACzB,GAAG,IAAI;gCACP,YAAY,WAAW,aAAa,WAAW;4BACjD,CAAC;;gBACH;;YAEA;2CAAW,CAAC;oBACV;mDAAkB,CAAA,OAAQ,CAAC;gCACzB,GAAG,IAAI;gCACP,WAAW;uCAAI,KAAK,SAAS;oCAAE;iCAAI;4BACrC,CAAC;;gBACH;;QACF;kCAAG,EAAE;IAEL,MAAM,WAAW;QACf,kBAAkB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,YAAY;YAAK,CAAC;QACxD,WAAW,mBAAmB,eAAe,eAAe;QAE5D,qDAAqD;QACrD,WAAW;YACT,kBAAkB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,YAAY;gBAAM,CAAC;QAC3D,GAAG;IACL;IAEA,MAAM,mBAAmB,CAAC,QAAgB;QACxC,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,SACR;oBAAE,GAAG,IAAI;oBAAE;oBAAS,cAAc,IAAI;gBAAO,IAC7C;IAER;IAEA,MAAM,mBAAmB,CAAC,MAAc;QACtC,MAAM,UAAoB;YACxB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB;YACA,MAAM,CAAC,KAAK,EAAE,MAAM;YACpB,SAAS,uBAAuB;YAChC;YACA,cAAc,IAAI;QACpB;QACA,SAAS,CAAA,OAAQ;mBAAI;gBAAM;aAAQ;QACnC,gBAAgB,QAAQ,EAAE;IAC5B;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,MAAM,MAAM,IAAI,GAAG,QAAQ,6BAA6B;QAE5D,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACjD,IAAI,iBAAiB,QAAQ;YAC3B,gBAAgB,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,MAAM;QAC1D;IACF;IAEA,MAAM,oBAAoB,OAAO,SAAiB;QAChD,MAAM,cAA2B;YAC/B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN;YACA,WAAW,IAAI;YACf;QACF;QAEA,kBAAkB,CAAA,OAAQ,CAAC;gBACzB,GAAG,IAAI;gBACP,cAAc;uBAAI,KAAK,YAAY;oBAAE;iBAAY;YACnD,CAAC;QAED,+CAA+C;QAC/C,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC5C,MAAM,WAAW,MAAM,gIAAA,CAAA,YAAS,CAAC,WAAW,CAAC;gBAC3C,SAAS;gBACT,SAAS;oBACP,aAAa,YAAY;oBACzB,UAAU,eAAe,eAAe;gBAC1C;YACF;YAEA,MAAM,YAAyB;gBAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS,SAAS,OAAO;gBACzB,WAAW,IAAI;gBACf,UAAU;oBACR,YAAY,SAAS,UAAU;gBACjC;YACF;YAEA,kBAAkB,CAAA,OAAQ,CAAC;oBACzB,GAAG,IAAI;oBACP,cAAc;2BAAI,KAAK,YAAY;wBAAE;qBAAU;gBACjD,CAAC;YAED,0CAA0C;YAC1C,IAAI,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,GAAG;gBAC3D,SAAS,WAAW,CAAC,OAAO,CAAC,CAAA;oBAC3B,IAAI,OAAO,MAAM,KAAK,UAAU;wBAC9B,iBAAiB,OAAO,IAAI,EAAE;oBAChC,OAAO,IAAI,OAAO,MAAM,KAAK,YAAY,OAAO,OAAO,EAAE;wBACvD,MAAM,aAAa,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,OAAO,IAAI;wBACzD,IAAI,YAAY;4BACd,iBAAiB,WAAW,EAAE,EAAE,OAAO,OAAO;wBAChD;oBACF;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC;;;;;;;;;;4BAUY,CAAC;YACvB,KAAK;gBACH,OAAO,CAAC;;;;;;;;;;4BAUY,CAAC;YACvB,KAAK;gBACH,OAAO,CAAC;;;;;;;;;;CAUf,CAAC;YACI,KAAK;gBACH,OAAO,CAAC;;;;;;;CAOf,CAAC;YACI,KAAK;gBACH,OAAO,CAAC;;;;;;;;;;CAUf,CAAC;YACI;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC5C,MAAM,UAAU,YAAY,WAAW;IAEvC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;;0DAC1C,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAI1C,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAE,WAAU;;gDACV,YAAY,QAAQ;gDAAY;;;;;;;;;;;;;;;;;;;sCAIvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;oCAAU,UAAU,eAAe,UAAU;;wCAC7E,eAAe,UAAU,iBAAG,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;iEAAoB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACjF,eAAe,UAAU,GAAG,gBAAgB;;;;;;;8CAE/C,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;;sDACd,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;8CACd,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAM1B,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAI9C,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC,8IAAA,CAAA,gBAAa;4CACZ,UAAU,eAAe,YAAY;4CACrC,eAAe;4CACf,YAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAOlB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAIrC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC,qJAAA,CAAA,aAAU;4CACT,OAAO;4CACP,cAAc;4CACd,cAAc;4CACd,cAAc;4CACd,cAAc;4CACd,cAAc;4CACd,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAOlB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAS,eAAe,eAAe,KAAK,YAAY,YAAY;4DACpE,MAAK;4DACL,SAAS,IAAM,kBAAkB,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,iBAAiB;oEAAU,CAAC;sEAClF;;;;;;sEAGD,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAS,eAAe,eAAe,KAAK,QAAQ,YAAY;4DAChE,MAAK;4DACL,SAAS,IAAM,kBAAkB,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,iBAAiB;oEAAM,CAAC;sEAC9E;;;;;;;;;;;;;;;;;;;;;;;kDAMP,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC,6JAAA,CAAA,kBAAe;gDACd,UAAU,eAAe,eAAe;gDACxC,SAAS;gDACT,eAAe,CAAC;oDACd,QAAQ,GAAG,CAAC,0BAA0B;gDACxC;;;;;;4CAID,eAAe,UAAU,kBACxB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAE,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUrC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;8DACnD,6LAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;sDAEzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;8DACnD,6LAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;sDAEzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,6LAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;sDAEzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,6LAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzC;GA9cwB;;QAsH4B,8IAAA,CAAA,eAAY;;;KAtHxC", "debugId": null}}]}