{"version": 3, "sources": ["../../../src/build/turborepo-access-trace/env.ts"], "sourcesContent": ["import type { EnvVars, RestoreOriginalFunction } from './types'\n\n/**\n * Proxy the environment to track environment variables keys that\n * are accessed during the build.\n *\n * @param envVars A set to track environment variable keys that are accessed.\n * @returns A function that restores the original environment.\n */\nexport function envProxy(envVars: EnvVars): RestoreOriginalFunction {\n  const newEnv = new Proxy(process.env, {\n    get: (target, key, receiver) => {\n      envVars.add(key)\n      return Reflect.get(target, key, receiver)\n    },\n    set: (target, key, value) => {\n      return Reflect.set(target, key, value)\n    },\n  })\n\n  const oldEnv = process.env\n  process.env = newEnv\n\n  // Return a function that restores the original environment.\n  return () => {\n    process.env = oldEnv\n  }\n}\n"], "names": ["envProxy", "envVars", "newEnv", "Proxy", "process", "env", "get", "target", "key", "receiver", "add", "Reflect", "set", "value", "oldEnv"], "mappings": ";;;;+BASgBA;;;eAAAA;;;AAAT,SAASA,SAASC,OAAgB;IACvC,MAAMC,SAAS,IAAIC,MAAMC,QAAQC,GAAG,EAAE;QACpCC,KAAK,CAACC,QAAQC,KAAKC;YACjBR,QAAQS,GAAG,CAACF;YACZ,OAAOG,QAAQL,GAAG,CAACC,QAAQC,KAAKC;QAClC;QACAG,KAAK,CAACL,QAAQC,KAAKK;YACjB,OAAOF,QAAQC,GAAG,CAACL,QAAQC,KAAKK;QAClC;IACF;IAEA,MAAMC,SAASV,QAAQC,GAAG;IAC1BD,QAAQC,GAAG,GAAGH;IAEd,4DAA4D;IAC5D,OAAO;QACLE,QAAQC,GAAG,GAAGS;IAChB;AACF"}