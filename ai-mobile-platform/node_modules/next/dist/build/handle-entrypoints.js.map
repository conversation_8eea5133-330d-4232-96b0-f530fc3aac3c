{"version": 3, "sources": ["../../src/build/handle-entrypoints.ts"], "sourcesContent": ["import type { TurbopackManifestLoader } from '../shared/lib/turbopack/manifest-loader'\nimport type {\n  Entrypoints,\n  PageRoute,\n  AppRoute,\n  RawEntrypoints,\n} from './swc/types'\nimport { getEntryKey } from '../shared/lib/turbopack/entry-key'\nimport * as Log from './output/log'\n\nexport async function rawEntrypointsToEntrypoints(\n  entrypointsOp: RawEntrypoints\n): Promise<Entrypoints> {\n  const page = new Map()\n  const app = new Map()\n\n  for (const [pathname, route] of entrypointsOp.routes) {\n    switch (route.type) {\n      case 'page':\n      case 'page-api':\n        page.set(pathname, route)\n        break\n      case 'app-page': {\n        for (const p of route.pages) {\n          app.set(p.originalName, {\n            type: 'app-page',\n            ...p,\n          })\n        }\n        break\n      }\n      case 'app-route': {\n        app.set(route.originalName, route)\n        break\n      }\n      default:\n        Log.info(`skipping ${pathname} (${route.type})`)\n        break\n    }\n  }\n\n  return {\n    global: {\n      app: entrypointsOp.pagesAppEndpoint,\n      document: entrypointsOp.pagesDocumentEndpoint,\n      error: entrypointsOp.pagesErrorEndpoint,\n      instrumentation: entrypointsOp.instrumentation,\n      middleware: entrypointsOp.middleware,\n    },\n    page,\n    app,\n  }\n}\n\nexport async function handleRouteType({\n  page,\n  route,\n  manifestLoader,\n}: {\n  page: string\n  route: PageRoute | AppRoute\n  manifestLoader: TurbopackManifestLoader\n}) {\n  const shouldCreateWebpackStats = process.env.TURBOPACK_STATS != null\n\n  switch (route.type) {\n    case 'page': {\n      const serverKey = getEntryKey('pages', 'server', page)\n\n      await manifestLoader.loadBuildManifest(page)\n      await manifestLoader.loadPagesManifest(page)\n\n      const middlewareManifestWritten =\n        await manifestLoader.loadMiddlewareManifest(page, 'pages')\n      if (!middlewareManifestWritten) {\n        manifestLoader.deleteMiddlewareManifest(serverKey)\n      }\n\n      await manifestLoader.loadFontManifest('/_app', 'pages')\n      await manifestLoader.loadFontManifest(page, 'pages')\n\n      if (shouldCreateWebpackStats) {\n        await manifestLoader.loadWebpackStats(page, 'pages')\n      }\n\n      break\n    }\n    case 'page-api': {\n      const key = getEntryKey('pages', 'server', page)\n\n      await manifestLoader.loadPagesManifest(page)\n      const middlewareManifestWritten =\n        await manifestLoader.loadMiddlewareManifest(page, 'pages')\n      if (!middlewareManifestWritten) {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      break\n    }\n    case 'app-page': {\n      const key = getEntryKey('app', 'server', page)\n\n      const middlewareManifestWritten =\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n      if (!middlewareManifestWritten) {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.loadAppBuildManifest(page)\n      await manifestLoader.loadBuildManifest(page, 'app')\n      await manifestLoader.loadAppPathsManifest(page)\n      await manifestLoader.loadActionManifest(page)\n      await manifestLoader.loadFontManifest(page, 'app')\n\n      if (shouldCreateWebpackStats) {\n        await manifestLoader.loadWebpackStats(page, 'app')\n      }\n\n      break\n    }\n    case 'app-route': {\n      const key = getEntryKey('app', 'server', page)\n\n      await manifestLoader.loadAppPathsManifest(page)\n\n      const middlewareManifestWritten =\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n\n      if (!middlewareManifestWritten) {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      break\n    }\n    default: {\n      throw new Error(`unknown route type ${(route as any).type} for ${page}`)\n    }\n  }\n}\n"], "names": ["handleRouteType", "rawEntrypointsToEntrypoints", "entrypointsOp", "page", "Map", "app", "pathname", "route", "routes", "type", "set", "p", "pages", "originalName", "Log", "info", "global", "pagesAppEndpoint", "document", "pagesDocumentEndpoint", "error", "pagesErrorEndpoint", "instrumentation", "middleware", "manifest<PERSON><PERSON>der", "shouldCreateWebpackStats", "process", "env", "TURBOPACK_STATS", "server<PERSON>ey", "getEntry<PERSON>ey", "loadBuildManifest", "loadPagesManifest", "middlewareManifestWritten", "loadMiddlewareManifest", "deleteMiddlewareManifest", "loadFontManifest", "loadWebpackStats", "key", "loadAppBuildManifest", "loadAppPathsManifest", "loadActionManifest", "Error"], "mappings": ";;;;;;;;;;;;;;;IAsDsBA,eAAe;eAAfA;;IA5CAC,2BAA2B;eAA3BA;;;0BAHM;6DACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEd,eAAeA,4BACpBC,aAA6B;IAE7B,MAAMC,OAAO,IAAIC;IACjB,MAAMC,MAAM,IAAID;IAEhB,KAAK,MAAM,CAACE,UAAUC,MAAM,IAAIL,cAAcM,MAAM,CAAE;QACpD,OAAQD,MAAME,IAAI;YAChB,KAAK;YACL,KAAK;gBACHN,KAAKO,GAAG,CAACJ,UAAUC;gBACnB;YACF,KAAK;gBAAY;oBACf,KAAK,MAAMI,KAAKJ,MAAMK,KAAK,CAAE;wBAC3BP,IAAIK,GAAG,CAACC,EAAEE,YAAY,EAAE;4BACtBJ,MAAM;4BACN,GAAGE,CAAC;wBACN;oBACF;oBACA;gBACF;YACA,KAAK;gBAAa;oBAChBN,IAAIK,GAAG,CAACH,MAAMM,YAAY,EAAEN;oBAC5B;gBACF;YACA;gBACEO,KAAIC,IAAI,CAAC,CAAC,SAAS,EAAET,SAAS,EAAE,EAAEC,MAAME,IAAI,CAAC,CAAC,CAAC;gBAC/C;QACJ;IACF;IAEA,OAAO;QACLO,QAAQ;YACNX,KAAKH,cAAce,gBAAgB;YACnCC,UAAUhB,cAAciB,qBAAqB;YAC7CC,OAAOlB,cAAcmB,kBAAkB;YACvCC,iBAAiBpB,cAAcoB,eAAe;YAC9CC,YAAYrB,cAAcqB,UAAU;QACtC;QACApB;QACAE;IACF;AACF;AAEO,eAAeL,gBAAgB,EACpCG,IAAI,EACJI,KAAK,EACLiB,cAAc,EAKf;IACC,MAAMC,2BAA2BC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAEhE,OAAQrB,MAAME,IAAI;QAChB,KAAK;YAAQ;gBACX,MAAMoB,YAAYC,IAAAA,qBAAW,EAAC,SAAS,UAAU3B;gBAEjD,MAAMqB,eAAeO,iBAAiB,CAAC5B;gBACvC,MAAMqB,eAAeQ,iBAAiB,CAAC7B;gBAEvC,MAAM8B,4BACJ,MAAMT,eAAeU,sBAAsB,CAAC/B,MAAM;gBACpD,IAAI,CAAC8B,2BAA2B;oBAC9BT,eAAeW,wBAAwB,CAACN;gBAC1C;gBAEA,MAAML,eAAeY,gBAAgB,CAAC,SAAS;gBAC/C,MAAMZ,eAAeY,gBAAgB,CAACjC,MAAM;gBAE5C,IAAIsB,0BAA0B;oBAC5B,MAAMD,eAAea,gBAAgB,CAAClC,MAAM;gBAC9C;gBAEA;YACF;QACA,KAAK;YAAY;gBACf,MAAMmC,MAAMR,IAAAA,qBAAW,EAAC,SAAS,UAAU3B;gBAE3C,MAAMqB,eAAeQ,iBAAiB,CAAC7B;gBACvC,MAAM8B,4BACJ,MAAMT,eAAeU,sBAAsB,CAAC/B,MAAM;gBACpD,IAAI,CAAC8B,2BAA2B;oBAC9BT,eAAeW,wBAAwB,CAACG;gBAC1C;gBAEA;YACF;QACA,KAAK;YAAY;gBACf,MAAMA,MAAMR,IAAAA,qBAAW,EAAC,OAAO,UAAU3B;gBAEzC,MAAM8B,4BACJ,MAAMT,eAAeU,sBAAsB,CAAC/B,MAAM;gBACpD,IAAI,CAAC8B,2BAA2B;oBAC9BT,eAAeW,wBAAwB,CAACG;gBAC1C;gBAEA,MAAMd,eAAee,oBAAoB,CAACpC;gBAC1C,MAAMqB,eAAeO,iBAAiB,CAAC5B,MAAM;gBAC7C,MAAMqB,eAAegB,oBAAoB,CAACrC;gBAC1C,MAAMqB,eAAeiB,kBAAkB,CAACtC;gBACxC,MAAMqB,eAAeY,gBAAgB,CAACjC,MAAM;gBAE5C,IAAIsB,0BAA0B;oBAC5B,MAAMD,eAAea,gBAAgB,CAAClC,MAAM;gBAC9C;gBAEA;YACF;QACA,KAAK;YAAa;gBAChB,MAAMmC,MAAMR,IAAAA,qBAAW,EAAC,OAAO,UAAU3B;gBAEzC,MAAMqB,eAAegB,oBAAoB,CAACrC;gBAE1C,MAAM8B,4BACJ,MAAMT,eAAeU,sBAAsB,CAAC/B,MAAM;gBAEpD,IAAI,CAAC8B,2BAA2B;oBAC9BT,eAAeW,wBAAwB,CAACG;gBAC1C;gBAEA;YACF;QACA;YAAS;gBACP,MAAM,qBAAkE,CAAlE,IAAII,MAAM,CAAC,mBAAmB,EAAE,AAACnC,MAAcE,IAAI,CAAC,KAAK,EAAEN,MAAM,GAAjE,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiE;YACzE;IACF;AACF"}